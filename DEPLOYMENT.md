# 🚀 Deployment Guide

This guide covers database setup and deployment for the Compliance Command Center DBOS application.

## 📋 Overview

The application includes comprehensive database setup and mock data loading capabilities that can be executed during deployment through multiple methods:

1. **DBOS Configuration** - Automated hooks in `dbos-config.yaml`
2. **Deployment Script** - Manual deployment with `deploy.sh`
3. **NPM Scripts** - Individual setup commands
4. **Manual Setup** - Step-by-step database configuration

## 🔧 Database Setup Components

### Schema Files
- **`database_schema.sql`** - Main database schema (15+ tables, indexes, constraints)
- **`migrations/001_initial_schema.sql`** - Migration-based schema setup
- **`scripts/load_mock_data.sql`** - Comprehensive mock data (50+ records)

### Setup Scripts
- **`deploy.sh`** - Complete deployment automation
- **`setup_database.sh`** - Database-only setup
- **`quick_setup.sh`** - Quick development setup
- **`scripts/load_mock_data.js`** - Mock data loader with validation

## 🎯 Deployment Methods

### Method 1: DBOS Automated Deployment

The `dbos-config.yaml` includes predeploy and postdeploy hooks:

```yaml
runtimeConfig:
  predeploy:
    - name: "Apply Database Schema"
      command: "psql ${DBOS_DATABASE_URL} -f database_schema.sql -v ON_ERROR_STOP=1"
    - name: "Load Mock Data"
      command: "npm run load-mock-data"
      condition: "${NODE_ENV} != 'production'"
    - name: "Verify Database Setup"
      command: "psql ${DBOS_DATABASE_URL} -c 'SELECT COUNT(*) FROM information_schema.tables...'"
```

**Usage:**
```bash
# Set environment
export DBOS_DATABASE_URL="postgresql://user:password@host:port/database"

# Deploy with DBOS
dbos deploy
```

### Method 2: Deployment Script

Use the comprehensive `deploy.sh` script:

```bash
# Development deployment (includes mock data)
./deploy.sh

# Production deployment (no mock data)
./deploy.sh --production

# Skip mock data
./deploy.sh --skip-mock-data

# Force schema update
./deploy.sh --force-schema
```

**Script Features:**
- ✅ Environment validation
- ✅ Database connectivity testing
- ✅ Schema application with error handling
- ✅ Conditional mock data loading
- ✅ Database verification
- ✅ Application building
- ✅ Comprehensive logging

### Method 3: NPM Scripts

Individual deployment steps:

```bash
# Quick setup (recommended for development)
npm run setup:quick

# Apply database schema only
npm run setup:db

# Load mock data
npm run load-mock-data

# Verify database setup
npm run verify:db

# Full deployment
npm run deploy

# Production deployment
npm run deploy:production
```

### Method 4: Manual Setup

Step-by-step manual deployment:

```bash
# 1. Create database
createdb dbos_kyc_demo

# 2. Apply schema
psql $DBOS_DATABASE_URL -f database_schema.sql

# 3. Load mock data (optional)
npm run load-mock-data

# 4. Verify setup
psql $DBOS_DATABASE_URL -c "SELECT COUNT(*) FROM compliance_rules;"

# 5. Build application
npm run build

# 6. Start application
npm start
```

## 🌍 Environment Configuration

### Required Environment Variables

```bash
# Database connection (required)
export DBOS_DATABASE_URL="postgresql://user:password@host:port/database"

# Optional configuration
export NODE_ENV="development"          # or "production"
export DB_NAME="dbos_kyc_demo"        # Database name
export SKIP_MOCK_DATA="false"         # Skip mock data loading
export FORCE_SCHEMA_UPDATE="false"    # Force schema updates
```

### Environment-Specific Behavior

**Development:**
- Mock data is loaded by default
- Detailed logging enabled
- Schema updates allowed

**Production:**
- Mock data is skipped by default
- Minimal logging
- Schema updates require explicit confirmation

## 📊 Database Schema Overview

The database includes:

### Core Tables (15+)
- `compliance_documents` - Document storage and metadata
- `compliance_rules` - Regulatory compliance rules
- `compliance_violations` - Violation tracking
- `kyc_profiles` - Customer KYC data (encrypted)
- `compliance_reports` - Generated reports
- `regulatory_updates` - Regulatory change tracking
- `workflow_executions` - DBOS workflow tracking
- `audit_logs` - System audit trail

### Mock Data Categories (50+ records)
- Compliance rules (SEC, GLBA, SOX, GDPR, CCPA)
- Sample documents with violations
- KYC customer profiles
- Compliance reports
- Regulatory updates
- Workflow execution history
- System audit logs

## 🔍 Verification & Testing

### Database Health Checks

```bash
# Check table count
psql $DBOS_DATABASE_URL -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';"

# Verify compliance rules
psql $DBOS_DATABASE_URL -c "SELECT COUNT(*) FROM compliance_rules WHERE is_active = true;"

# Check mock data
psql $DBOS_DATABASE_URL -c "SELECT COUNT(*) FROM compliance_documents;"

# Test application connectivity
curl http://localhost:3000/health
```

### Application Testing

```bash
# Run tests
npm test

# Test SQL syntax
npm run test-sql

# Load mock data with verification
npm run load-mock-data:force
```

## 🛠️ Troubleshooting

### Common Issues

**Database Connection Failed**
```bash
# Check PostgreSQL is running
pg_ctl status

# Test connection
psql $DBOS_DATABASE_URL -c "SELECT version();"
```

**Permission Denied**
```bash
# Grant permissions
psql $DBOS_DATABASE_URL -c "GRANT ALL ON ALL TABLES IN SCHEMA public TO your_user;"
```

**Schema Application Failed**
```bash
# Check for existing data conflicts
psql $DBOS_DATABASE_URL -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public';"

# Force schema update
./deploy.sh --force-schema
```

**Mock Data Loading Failed**
```bash
# Skip mock data
./deploy.sh --skip-mock-data

# Or load manually with verbose output
npm run load-mock-data:force
```

## 🔐 Security Considerations

### Production Deployment
- Use strong database credentials
- Enable SSL/TLS for database connections
- Restrict database access to application servers only
- Regular security updates and patches

### Data Protection
- KYC data is encrypted at rest
- Audit logs track all data access
- Compliance with GDPR, CCPA requirements
- 7-year data retention policy

## 📈 Performance Optimization

### Database Indexes
- Optimized queries with proper indexing
- Foreign key constraints for data integrity
- Partitioning for large tables (future enhancement)

### Monitoring
- Workflow execution tracking
- Performance metrics collection
- Automated health checks

## 🎉 Success Indicators

After successful deployment, you should see:

✅ **Database:** 15+ tables created  
✅ **Rules:** 10+ active compliance rules  
✅ **Data:** 50+ mock records (development)  
✅ **Application:** Running on http://localhost:3000  
✅ **Health Check:** http://localhost:3000/health returns 200  
✅ **API:** All endpoints responding correctly  

## 📞 Support

For deployment issues:
1. Check the troubleshooting section above
2. Review application logs
3. Verify environment configuration
4. Test database connectivity independently

The deployment system is designed to be robust and provide clear error messages to help diagnose and resolve issues quickly.
