#!/bin/bash

# Deployment Script for Compliance Command Center DBOS
# This script handles database setup and mock data loading for deployment

set -e  # Exit on any error

# Configuration
DB_NAME="${DB_NAME:-dbos_kyc_demo}"
NODE_ENV="${NODE_ENV:-development}"
SKIP_MOCK_DATA="${SKIP_MOCK_DATA:-false}"
FORCE_SCHEMA_UPDATE="${FORCE_SCHEMA_UPDATE:-false}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# Check if required environment variables are set
check_environment() {
    log_step "Checking environment configuration..."
    
    if [[ -z "$DBOS_DATABASE_URL" ]]; then
        log_error "DBOS_DATABASE_URL environment variable is not set"
        log_error "Please set it to your PostgreSQL connection string:"
        log_error "export DBOS_DATABASE_URL='postgresql://user:password@host:port/database'"
        exit 1
    fi
    
    log_success "Database URL configured: ${DBOS_DATABASE_URL%%@*}@***"
    log_info "Environment: $NODE_ENV"
    log_info "Database: $DB_NAME"
}

# Test database connectivity
test_database_connection() {
    log_step "Testing database connectivity..."
    
    if psql "$DBOS_DATABASE_URL" -c "SELECT version();" > /dev/null 2>&1; then
        log_success "Database connection successful"
    else
        log_error "Failed to connect to database"
        log_error "Please check your DBOS_DATABASE_URL and ensure PostgreSQL is running"
        exit 1
    fi
}

# Check if schema files exist
check_schema_files() {
    log_step "Checking schema files..."
    
    if [[ ! -f "database_schema.sql" ]]; then
        log_error "database_schema.sql not found"
        log_error "Please ensure you're running this script from the project root directory"
        exit 1
    fi
    
    if [[ ! -f "migrations/001_initial_schema.sql" ]]; then
        log_warning "migrations/001_initial_schema.sql not found"
        log_warning "Migration file is optional but recommended for production"
    fi
    
    log_success "Schema files found"
}

# Apply database schema
apply_database_schema() {
    log_step "Applying database schema..."
    
    # Check if we should force schema update
    if [[ "$FORCE_SCHEMA_UPDATE" == "true" ]]; then
        log_warning "Force schema update enabled - this may overwrite existing data"
    fi
    
    # Apply the main schema
    if psql "$DBOS_DATABASE_URL" -f database_schema.sql -v ON_ERROR_STOP=1; then
        log_success "Database schema applied successfully"
    else
        log_error "Failed to apply database schema"
        exit 1
    fi
}

# Load mock data
load_mock_data() {
    log_step "Loading mock data..."
    
    # Skip mock data in production unless explicitly requested
    if [[ "$NODE_ENV" == "production" && "$SKIP_MOCK_DATA" != "false" ]]; then
        log_info "Skipping mock data loading in production environment"
        return 0
    fi
    
    if [[ "$SKIP_MOCK_DATA" == "true" ]]; then
        log_info "Skipping mock data loading (SKIP_MOCK_DATA=true)"
        return 0
    fi
    
    # Check if mock data script exists
    if [[ ! -f "scripts/load_mock_data.js" ]]; then
        log_warning "Mock data script not found, skipping mock data loading"
        return 0
    fi
    
    # Load mock data
    if npm run load-mock-data; then
        log_success "Mock data loaded successfully"
    else
        log_warning "Failed to load mock data (non-critical error)"
        log_warning "Application will still function but with empty database"
    fi
}

# Verify database setup
verify_database_setup() {
    log_step "Verifying database setup..."
    
    # Count tables
    TABLE_COUNT=$(psql "$DBOS_DATABASE_URL" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';" | tr -d ' ')
    
    if [[ $TABLE_COUNT -gt 10 ]]; then
        log_success "Database verification passed: $TABLE_COUNT tables found"
    else
        log_error "Database verification failed: only $TABLE_COUNT tables found"
        log_error "Expected at least 10 tables for proper application functionality"
        exit 1
    fi
    
    # Check compliance rules
    RULE_COUNT=$(psql "$DBOS_DATABASE_URL" -t -c "SELECT COUNT(*) FROM compliance_rules WHERE is_active = true;" 2>/dev/null | tr -d ' ' || echo "0")
    
    if [[ $RULE_COUNT -gt 0 ]]; then
        log_success "Compliance rules verified: $RULE_COUNT active rules found"
    else
        log_warning "No active compliance rules found - this may affect application functionality"
    fi
}

# Build application
build_application() {
    log_step "Building application..."
    
    if npm run build; then
        log_success "Application built successfully"
    else
        log_error "Failed to build application"
        exit 1
    fi
}

# Display deployment summary
display_summary() {
    echo
    echo "=============================================="
    echo "  Deployment Summary"
    echo "=============================================="
    echo -e "Database: ${GREEN}$DB_NAME${NC}"
    echo -e "Environment: ${GREEN}$NODE_ENV${NC}"
    echo -e "Schema Applied: ${GREEN}✓${NC}"
    echo -e "Mock Data: ${GREEN}$([ "$SKIP_MOCK_DATA" == "true" ] && echo "Skipped" || echo "Loaded")${NC}"
    echo -e "Application Built: ${GREEN}✓${NC}"
    echo
    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
    echo
    echo "Next steps:"
    echo "1. Start the application: npm start"
    echo "2. Access the application at: http://localhost:3000"
    echo "3. Check health endpoint: http://localhost:3000/health"
    echo
}

# Main deployment function
main() {
    echo "=============================================="
    echo "  Compliance Command Center DBOS Deployment"
    echo "=============================================="
    echo
    
    check_environment
    test_database_connection
    check_schema_files
    apply_database_schema
    load_mock_data
    verify_database_setup
    build_application
    display_summary
}

# Handle script arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-mock-data)
            SKIP_MOCK_DATA="true"
            shift
            ;;
        --force-schema)
            FORCE_SCHEMA_UPDATE="true"
            shift
            ;;
        --production)
            NODE_ENV="production"
            SKIP_MOCK_DATA="true"
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo
            echo "Options:"
            echo "  --skip-mock-data    Skip loading mock data"
            echo "  --force-schema      Force schema update (may overwrite data)"
            echo "  --production        Set production mode (skips mock data)"
            echo "  --help              Show this help message"
            echo
            echo "Environment Variables:"
            echo "  DBOS_DATABASE_URL   PostgreSQL connection string (required)"
            echo "  NODE_ENV            Environment (development/production)"
            echo "  DB_NAME             Database name (default: dbos_kyc_demo)"
            echo "  SKIP_MOCK_DATA      Skip mock data loading (true/false)"
            echo "  FORCE_SCHEMA_UPDATE Force schema update (true/false)"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Run main deployment
main
