# Comprehensive Guide for New Developers: Compliance Command Center DBOS

## Introduction

Welcome to the Compliance Command Center DBOS project! This guide will help you understand the project structure, set up your development environment, and get familiar with the key components of
this compliance management system built on the DBOS framework.

## System Overview

The Compliance Command Center is a comprehensive application that provides:
• Regulatory compliance monitoring
• KYC (Know Your Customer) processing
• Document analysis and management
• Compliance reporting and analytics

### Technology Stack

**Frontend:**
• React 18 with TypeScript
• Vite for development and building
• Tailwind CSS for styling
• Radix UI components
• React Router for navigation
• React Hook Form for form handling
• Recharts for data visualization

**Backend:**
• Node.js with TypeScript
• DBOS framework for workflow orchestration
• Express.js for API endpoints
• PostgreSQL database with pgcrypto for encryption
• Multer for file uploads

**Development Tools:**
• Jest for testing
• ESLint for code linting
• Concurrently for running multiple processes

## Development Environment Setup

### Prerequisites

1. Install the following:
   • Node.js 18+ and npm/bun
   • PostgreSQL 12+ with UUID and pgcrypto extensions
   • Git

2. Clone the repository:
```bash
git clone https://github.com/otrajman/compliance-command-center-dbos.git
cd compliance-command-center-dbos
```

3. Install dependencies:
```bash
npm install
# or
bun install
```


### Database Setup

**Note**: The default database name is `dbos_kyc_demo` for development. You can use any name you prefer.

1. Quick setup option (recommended):
```bash
./quick_setup.sh
```

2. Manual setup:
```bash
createdb dbos_kyc_demo
psql dbos_kyc_demo -f database_schema.sql
```

3. Configure environment:
```bash
export DBOS_DATABASE_URL="postgresql://dbosadmin@localhost:5432/dbos_kyc_demo"
```

**Alternative database names**: You can use `compliance_command_center` or any other name by updating the commands above and the environment variable accordingly.


### Running the Application

You have several options for running the application:

1. Run frontend and backend together (recommended for development):
```bash
npm run dev:full
```
This starts both the backend API server (port 3000) and frontend dev server (port 8080).

2. Run separately:
```bash
# Terminal 1 - Start the backend API server
npm run dev:backend

# Terminal 2 - Start the frontend development server
npm run dev
```

3. Production mode:
```bash
npm run start
```


Access points:
• Frontend: http://localhost:8080 (development)
• Backend API: http://localhost:3000
• Health check: http://localhost:3000/health

## Project Structure

```
compliance-command-center-dbos/
├── src/                     # Source code
│   ├── index.ts             # Main application entry point (backend)
│   ├── main.tsx             # Frontend React entry point
│   ├── App.tsx              # Main React component
│   ├── workflows/           # DBOS workflow definitions
│   ├── services/            # Business logic services
│   ├── routes/              # API route handlers
│   ├── database/            # Database connection and utilities
│   ├── components/          # React UI components
│   ├── pages/               # React page components
│   ├── hooks/               # React custom hooks
│   ├── types/               # TypeScript type definitions
│   ├── lib/                 # Utility libraries
│   ├── middleware/          # Express middleware
│   ├── config/              # Configuration files
│   └── data/                # Static data and demo content
├── public/                  # Static assets (favicon, etc.)
├── migrations/              # Database migration files
├── scripts/                 # Utility scripts
├── test/                    # Test files
├── uploads/                 # File upload directory
└── dist/                    # Built frontend assets
```


## Key Components

### Database Schema

The application uses PostgreSQL with these key tables:
• compliance_documents: Stores documents for compliance checking
• compliance_rules: Defines compliance rules and patterns
• kyc_profiles: Stores KYC profiles with encrypted personal information
• compliance_violations: Records violations found during compliance checks
• regulatory_updates: Tracks regulatory changes and updates
• workflow_executions: Monitors DBOS workflow executions
• audit_logs: Maintains comprehensive audit trails

For detailed schema information, refer to DATABASE_SCHEMA.md.

### API Endpoints

The application provides RESTful API endpoints organized by functionality:
• Document management: /api/documents/*
• KYC processing: /api/kyc/*
• Compliance reporting: /api/reports/*
• Regulatory updates: /api/regulatory/*
• Dashboard analytics: /api/dashboard/*
• Workflow management: /api/workflows/*

### Frontend-Backend Integration

The application uses Vite for frontend development with automatic API proxying:
• **Frontend Dev Server**: Port 8080 (Vite with React)
• **Backend API Server**: Port 3000 (Express + DBOS)
• **API Proxy**: Vite automatically proxies `/api/*` requests to the backend
• **Technology Stack**: React + TypeScript frontend, Node.js + DBOS backend

## Development Workflow

1. Understanding the codebase:
   • **Backend**: Start by exploring the main entry point (`src/index.ts`)
   • **Frontend**: Review the React app structure starting from `src/main.tsx` and `src/App.tsx`
   • **Workflows**: Review the workflow definitions in the `src/workflows/` directory
   • **Database**: Examine the database schema to understand data relationships
   • **API Routes**: Check `src/routes/` for API endpoint implementations

2. Making changes:
   • **Backend changes**: Modify files in `src/routes/`, `src/workflows/`, `src/services/`, etc.
   • **Frontend changes**: Work in `src/components/`, `src/pages/`, `src/hooks/`, etc.
   • **Database changes**: Update schema files and create migrations
   • **Testing**: Run tests to ensure your changes don't break existing functionality

3. Testing your changes:
```bash
npm test
# or
bun test
```

For testing SQL syntax:
```bash
npm run test-sql
```

### Available NPM Scripts

The project includes several useful npm scripts:

```bash
# Development
npm run dev              # Start frontend dev server (port 8080)
npm run dev:backend      # Start backend server (port 3000)
npm run dev:full         # Start both frontend and backend

# Production
npm run start            # Start production server
npm run build            # Build for production
npm run build:dev        # Build for development

# Testing
npm test                 # Run all tests
npm run test:coverage    # Run tests with coverage report
npm run test-sql         # Test SQL syntax

# Utilities
npm run lint             # Run ESLint
npm run preview          # Preview production build
npm run load-mock-data   # Load mock data into database
```


## Current Project Status

The project is currently transitioning from static mock data to database-driven data. Refer to PHASE_2_IMPLEMENTATION_GUIDE.md for details on the remaining implementation tasks.

## Security Considerations

When working on this project, be aware of these security features:
• Personal information in KYC profiles is encrypted using pgcrypto
• Comprehensive audit logging for all critical operations
• Role-based access control for different user types
• Secure document handling and storage

## Demo and Testing Features

### Demo Documents
For documents and KYC center features, the application supports demo document uploads that demonstrate workflow results. These are designed to showcase the system's capabilities rather than process actual documents.

### File Upload Directory
The `uploads/` directory is used for temporary file storage during document processing. Ensure this directory has appropriate permissions for the application to write files.

## Troubleshooting

For common setup and configuration issues, refer to SETUP_TROUBLESHOOTING.md.

## Contributing Guidelines

1. Fork the repository
2. Create a feature branch (git checkout -b feature/amazing-feature)
3. Commit your changes (git commit -m 'Add some amazing feature')
4. Push to the branch (git push origin feature/amazing-feature)
5. Open a Pull Request

## Additional Resources

• Review the REFACTORING_SUMMARY.md for information about the transition from static mock data to database queries
• Check the performance considerations in the README for optimizing database operations
