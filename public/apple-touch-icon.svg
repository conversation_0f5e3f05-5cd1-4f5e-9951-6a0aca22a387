<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with rounded corners for Apple touch icon -->
  <defs>
    <linearGradient id="bgGradientApple" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="shieldGradientApple" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background with rounded corners -->
  <rect width="180" height="180" rx="32" ry="32" fill="url(#bgGradientApple)"/>
  
  <!-- Shield shape (scaled up) -->
  <path d="M90 30L60 42V84C60 103.5 71.25 120.95 90 129C108.75 120.95 120 103.5 120 84V42L90 30Z" 
        fill="url(#shieldGradientApple)" 
        stroke="#1e40af" 
        stroke-width="2"/>
  
  <!-- Checkmark (scaled up) -->
  <path d="M76.5 90L87.5 101L109.5 72" 
        stroke="#10b981" 
        stroke-width="8" 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        fill="none"/>
  
  <!-- Small dots representing data/monitoring (scaled up) -->
  <circle cx="68" cy="60" r="4" fill="#60a5fa" opacity="0.7"/>
  <circle cx="112" cy="60" r="4" fill="#60a5fa" opacity="0.7"/>
  <circle cx="68" cy="120" r="4" fill="#60a5fa" opacity="0.7"/>
  <circle cx="112" cy="120" r="4" fill="#60a5fa" opacity="0.7"/>
  
  <!-- Additional decorative elements for larger size -->
  <circle cx="45" cy="90" r="2" fill="#60a5fa" opacity="0.5"/>
  <circle cx="135" cy="90" r="2" fill="#60a5fa" opacity="0.5"/>
  <circle cx="90" cy="45" r="2" fill="#60a5fa" opacity="0.5"/>
  <circle cx="90" cy="135" r="2" fill="#60a5fa" opacity="0.5"/>
</svg>
