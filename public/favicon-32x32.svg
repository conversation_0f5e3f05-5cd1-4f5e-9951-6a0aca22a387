<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient32" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="shieldGradient32" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <circle cx="16" cy="16" r="16" fill="url(#bgGradient32)"/>
  
  <!-- Shield shape -->
  <path d="M16 4L10 7V14C10 18.5 12.5 22.26 16 23C19.5 22.26 22 18.5 22 14V7L16 4Z" 
        fill="url(#shieldGradient32)" 
        stroke="#1e40af" 
        stroke-width="0.5"/>
  
  <!-- Checkmark -->
  <path d="M13.5 15L15.5 17L19.5 12" 
        stroke="#10b981" 
        stroke-width="2" 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        fill="none"/>
  
  <!-- Small dots representing data/monitoring -->
  <circle cx="12" cy="10" r="0.8" fill="#60a5fa" opacity="0.7"/>
  <circle cx="20" cy="10" r="0.8" fill="#60a5fa" opacity="0.7"/>
  <circle cx="12" cy="20" r="0.8" fill="#60a5fa" opacity="0.7"/>
  <circle cx="20" cy="20" r="0.8" fill="#60a5fa" opacity="0.7"/>
</svg>
