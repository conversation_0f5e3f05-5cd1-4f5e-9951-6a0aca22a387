# Favicon Documentation

## Overview
This directory contains the favicon files for the Compliance Command Center application.

## Files

### Core Favicon Files
- `favicon.svg` - Modern SVG favicon (32x32, scalable)
- `favicon.ico` - Traditional ICO format for older browsers (16x16)
- `favicon-32x32.svg` - Explicit 32x32 SVG version
- `apple-touch-icon.svg` - Apple touch icon (180x180)

### Configuration
- `site.webmanifest` - Web app manifest for PWA support

## Design
The favicon features:
- **Shield shape** - Represents security and compliance protection
- **Green checkmark** - Indicates verification and approval
- **Blue gradient background** - Professional, trustworthy appearance
- **Small monitoring dots** - Represents data monitoring and command center functionality

## Colors
- Primary Blue: `#1e40af` to `#3b82f6` (gradient)
- Success Green: `#10b981` (checkmark)
- Light Blue: `#60a5fa` (accent dots)
- White/Gray: `#ffffff` to `#e5e7eb` (shield fill)

## Browser Support
- Modern browsers: SVG favicon with scalable vector graphics
- Legacy browsers: ICO fallback
- Apple devices: Dedicated touch icon
- PWA: Web manifest with multiple icon sizes

## Usage
The favicon is automatically loaded by browsers when the HTML includes the appropriate `<link>` tags in the `<head>` section.
