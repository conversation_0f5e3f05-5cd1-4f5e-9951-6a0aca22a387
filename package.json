{"name": "compliance-command-center-dbos", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "dev:backend": "npx tsx src/index.ts", "dev:full": "concurrently \"npm run dev:backend\" \"npm run dev\" --names \"backend,frontend\" --prefix-colors \"blue,green\"", "start": "npx tsx src/index.ts", "build": "vite build && npx tsc", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:coverage": "jest --coverage", "migrate": "node scripts/migrate.js", "predeploy": "node scripts/predeploy.js", "postdeploy": "node scripts/postdeploy.js", "load-mock-data": "node scripts/load_mock_data.js", "load-mock-data:force": "node scripts/load_mock_data.js --force --verbose", "test-sql": "node scripts/test_sql_syntax.js", "deploy": "./deploy.sh", "deploy:production": "./deploy.sh --production", "deploy:skip-mock": "./deploy.sh --skip-mock-data", "setup:db": "psql $DBOS_DATABASE_URL -f database_schema.sql -v ON_ERROR_STOP=1", "setup:quick": "./quick_setup.sh", "verify:db": "psql $DBOS_DATABASE_URL -c \"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';\""}, "dependencies": {"@dbos-inc/dbos-sdk": "^2.10.22", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "express": "^5.1.0", "fastify": "^5.3.3", "hono": "^4.7.11", "input-otp": "^1.2.4", "koa": "^3.0.0", "lucide-react": "^0.462.0", "multer": "^2.0.1", "next-themes": "^0.3.0", "node-fetch": "^3.3.2", "pg": "^8.13.1", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "ts-node-dev": "^2.0.0", "tsx": "^4.19.4", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/multer": "^1.4.13", "@types/node": "^22.15.30", "@types/pg": "^8.11.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/supertest": "^6.0.3", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jest": "^29.7.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "supertest": "^7.1.1", "tailwindcss": "^3.4.11", "ts-jest": "^29.3.4", "typescript": "^5.8.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}