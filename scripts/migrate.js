#!/usr/bin/env node

/**
 * Database Migration Script for Compliance Command Center DBOS
 * This script handles database schema migrations using Node.js and pg client
 */

import { Client } from 'pg';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const DATABASE_URL = process.env.DBOS_DATABASE_URL;
const MIGRATIONS_DIR = path.join(__dirname, '..', 'migrations');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(level, message) {
  const timestamp = new Date().toISOString();
  const colorMap = {
    info: colors.blue,
    success: colors.green,
    warning: colors.yellow,
    error: colors.red
  };
  
  console.log(`${colorMap[level] || ''}[${timestamp}] [${level.toUpperCase()}] ${message}${colors.reset}`);
}

async function connectToDatabase() {
  if (!DATABASE_URL) {
    throw new Error('DBOS_DATABASE_URL environment variable is required');
  }

  const client = new Client({
    connectionString: DATABASE_URL,
    ssl: DATABASE_URL.includes('sslmode=require') ? { rejectUnauthorized: false } : false
  });

  try {
    await client.connect();
    log('info', 'Connected to database successfully');
    return client;
  } catch (error) {
    log('error', `Failed to connect to database: ${error.message}`);
    throw error;
  }
}

async function readMigrationFile(filename) {
  try {
    const filePath = path.join(MIGRATIONS_DIR, filename);
    const content = await fs.readFile(filePath, 'utf8');
    log('info', `Read migration file: ${filename}`);
    return content;
  } catch (error) {
    log('error', `Failed to read migration file ${filename}: ${error.message}`);
    throw error;
  }
}

async function executeMigration(client, filename) {
  log('info', `Starting migration: ${filename}`);
  
  try {
    const sql = await readMigrationFile(filename);
    
    // Execute the migration within a transaction
    await client.query('BEGIN');
    
    try {
      await client.query(sql);
      await client.query('COMMIT');
      log('success', `Migration completed successfully: ${filename}`);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    }
    
  } catch (error) {
    log('error', `Migration failed: ${filename} - ${error.message}`);
    throw error;
  }
}

async function createMigrationsTable(client) {
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS dbos_migrations (
      id SERIAL PRIMARY KEY,
      filename VARCHAR(255) UNIQUE NOT NULL,
      executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
      checksum VARCHAR(64),
      execution_time_ms INTEGER
    );
  `;
  
  try {
    await client.query(createTableSQL);
    log('info', 'Migrations tracking table ready');
  } catch (error) {
    log('error', `Failed to create migrations table: ${error.message}`);
    throw error;
  }
}

async function isMigrationExecuted(client, filename) {
  try {
    const result = await client.query(
      'SELECT filename FROM dbos_migrations WHERE filename = $1',
      [filename]
    );
    return result.rows.length > 0;
  } catch (error) {
    log('error', `Failed to check migration status: ${error.message}`);
    throw error;
  }
}

async function recordMigration(client, filename, executionTime) {
  try {
    await client.query(
      'INSERT INTO dbos_migrations (filename, execution_time_ms) VALUES ($1, $2)',
      [filename, executionTime]
    );
    log('info', `Recorded migration: ${filename} (${executionTime}ms)`);
  } catch (error) {
    log('error', `Failed to record migration: ${error.message}`);
    throw error;
  }
}

async function getMigrationFiles() {
  try {
    const files = await fs.readdir(MIGRATIONS_DIR);
    return files
      .filter(file => file.endsWith('.sql'))
      .sort(); // Ensure migrations run in order
  } catch (error) {
    log('error', `Failed to read migrations directory: ${error.message}`);
    throw error;
  }
}

async function runMigrations() {
  let client;
  
  try {
    log('info', 'Starting database migrations...');
    
    client = await connectToDatabase();
    await createMigrationsTable(client);
    
    const migrationFiles = await getMigrationFiles();
    log('info', `Found ${migrationFiles.length} migration files`);
    
    let executedCount = 0;
    
    for (const filename of migrationFiles) {
      const isExecuted = await isMigrationExecuted(client, filename);
      
      if (isExecuted) {
        log('info', `Skipping already executed migration: ${filename}`);
        continue;
      }
      
      const startTime = Date.now();
      await executeMigration(client, filename);
      const executionTime = Date.now() - startTime;
      
      await recordMigration(client, filename, executionTime);
      executedCount++;
    }
    
    if (executedCount === 0) {
      log('info', 'No new migrations to execute');
    } else {
      log('success', `Successfully executed ${executedCount} migrations`);
    }
    
    // Verify database state
    const result = await client.query(`
      SELECT COUNT(*) as table_count 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
    `);
    
    log('success', `Database ready with ${result.rows[0].table_count} tables`);
    
  } catch (error) {
    log('error', `Migration process failed: ${error.message}`);
    process.exit(1);
  } finally {
    if (client) {
      await client.end();
      log('info', 'Database connection closed');
    }
  }
}

// Run migrations if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigrations().catch(error => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
}

export { runMigrations };
