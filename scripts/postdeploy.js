#!/usr/bin/env node

/**
 * Post-deployment Script for Compliance Command Center DBOS
 * This script handles post-deployment verification and health checks
 */

import { Client } from 'pg';

// Configuration
const DATABASE_URL = process.env.DBOS_DATABASE_URL;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(level, message) {
  const timestamp = new Date().toISOString();
  const colorMap = {
    info: colors.blue,
    success: colors.green,
    warning: colors.yellow,
    error: colors.red
  };
  
  console.log(`${colorMap[level] || ''}[${timestamp}] [${level.toUpperCase()}] ${message}${colors.reset}`);
}

async function connectToDatabase() {
  if (!DATABASE_URL) {
    throw new Error('DBOS_DATABASE_URL environment variable is required');
  }

  const client = new Client({
    connectionString: DATABASE_URL,
    ssl: DATABASE_URL.includes('sslmode=require') ? { rejectUnauthorized: false } : false
  });

  try {
    await client.connect();
    log('info', 'Connected to database successfully');
    return client;
  } catch (error) {
    log('error', `Failed to connect to database: ${error.message}`);
    throw error;
  }
}

async function performHealthCheck(client) {
  log('info', 'Performing database health check...');
  
  try {
    // Check database version and connectivity
    const versionResult = await client.query('SELECT version()');
    const timeResult = await client.query('SELECT NOW() as current_time');
    
    log('success', `Database version: ${versionResult.rows[0].version.split(' ').slice(0, 2).join(' ')}`);
    log('success', `Database time: ${timeResult.rows[0].current_time}`);
    
    // Check database performance
    const startTime = Date.now();
    await client.query('SELECT 1');
    const responseTime = Date.now() - startTime;
    
    if (responseTime > 1000) {
      log('warning', `Database response time is high: ${responseTime}ms`);
    } else {
      log('success', `Database response time: ${responseTime}ms`);
    }
    
    return true;
    
  } catch (error) {
    log('error', `Health check failed: ${error.message}`);
    throw error;
  }
}

async function validateComplianceRules(client) {
  log('info', 'Validating compliance rules configuration...');
  
  try {
    // Check active compliance rules
    const rulesResult = await client.query(`
      SELECT 
        standard,
        COUNT(*) as rule_count,
        COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_rules,
        COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_rules
      FROM compliance_rules 
      WHERE is_active = true 
      GROUP BY standard
      ORDER BY standard
    `);
    
    if (rulesResult.rows.length === 0) {
      throw new Error('No active compliance rules found');
    }
    
    let totalRules = 0;
    let criticalRules = 0;
    
    log('info', 'Compliance rules by standard:');
    for (const row of rulesResult.rows) {
      const ruleCount = parseInt(row.rule_count);
      const criticalCount = parseInt(row.critical_rules);
      const highCount = parseInt(row.high_rules);
      
      totalRules += ruleCount;
      criticalRules += criticalCount;
      
      log('info', `  ${row.standard}: ${ruleCount} rules (${criticalCount} critical, ${highCount} high)`);
    }
    
    log('success', `Total active compliance rules: ${totalRules} (${criticalRules} critical)`);
    
    // Validate compliance standards configuration
    const standardsResult = await client.query(`
      SELECT 
        standard,
        display_name,
        monitoring_enabled,
        notification_enabled
      FROM compliance_standards_config
      WHERE is_active = true
      ORDER BY standard
    `);
    
    log('info', 'Active compliance standards:');
    for (const row of standardsResult.rows) {
      const status = [];
      if (row.monitoring_enabled) status.push('monitoring');
      if (row.notification_enabled) status.push('notifications');
      
      log('info', `  ${row.standard} (${row.display_name}): ${status.join(', ')}`);
    }
    
    return true;
    
  } catch (error) {
    log('error', `Compliance rules validation failed: ${error.message}`);
    throw error;
  }
}

async function validateSystemConfiguration(client) {
  log('info', 'Validating system configuration...');
  
  try {
    const configResult = await client.query(`
      SELECT 
        category,
        COUNT(*) as config_count
      FROM system_configuration 
      WHERE is_active = true 
      GROUP BY category
      ORDER BY category
    `);
    
    if (configResult.rows.length === 0) {
      throw new Error('No system configuration found');
    }
    
    let totalConfigs = 0;
    log('info', 'System configuration by category:');
    for (const row of configResult.rows) {
      const count = parseInt(row.config_count);
      totalConfigs += count;
      log('info', `  ${row.category}: ${count} settings`);
    }
    
    log('success', `Total system configuration settings: ${totalConfigs}`);
    
    // Check critical configuration values
    const criticalConfigs = [
      'compliance.auto_scan_enabled',
      'kyc.risk_threshold_high',
      'kyc.risk_threshold_critical',
      'notifications.email_enabled'
    ];
    
    const criticalResult = await client.query(`
      SELECT config_key, config_value, config_type
      FROM system_configuration 
      WHERE config_key = ANY($1) AND is_active = true
    `, [criticalConfigs]);
    
    log('info', 'Critical configuration values:');
    for (const row of criticalResult.rows) {
      log('info', `  ${row.config_key}: ${row.config_value} (${row.config_type})`);
    }
    
    return true;
    
  } catch (error) {
    log('error', `System configuration validation failed: ${error.message}`);
    throw error;
  }
}

async function generateDeploymentReport(client) {
  log('info', 'Generating deployment report...');
  
  try {
    // Get database statistics
    const statsResult = await client.query(`
      SELECT 
        (SELECT COUNT(*) FROM compliance_documents) as documents,
        (SELECT COUNT(*) FROM compliance_rules WHERE is_active = true) as active_rules,
        (SELECT COUNT(*) FROM kyc_profiles) as kyc_profiles,
        (SELECT COUNT(*) FROM compliance_violations) as violations,
        (SELECT COUNT(*) FROM workflow_executions) as workflows,
        (SELECT COUNT(*) FROM audit_logs) as audit_logs
    `);
    
    const stats = statsResult.rows[0];
    
    log('success', '=== DEPLOYMENT REPORT ===');
    log('info', `Documents: ${stats.documents}`);
    log('info', `Active Rules: ${stats.active_rules}`);
    log('info', `KYC Profiles: ${stats.kyc_profiles}`);
    log('info', `Violations: ${stats.violations}`);
    log('info', `Workflows: ${stats.workflows}`);
    log('info', `Audit Logs: ${stats.audit_logs}`);
    log('success', '========================');
    
    return stats;
    
  } catch (error) {
    log('error', `Failed to generate deployment report: ${error.message}`);
    throw error;
  }
}

async function runPostDeploy() {
  let client;
  
  try {
    log('info', 'Starting post-deployment verification...');
    log('info', `Environment: ${NODE_ENV}`);
    
    client = await connectToDatabase();
    
    // Step 1: Perform health check
    await performHealthCheck(client);
    
    // Step 2: Validate compliance rules
    await validateComplianceRules(client);
    
    // Step 3: Validate system configuration
    await validateSystemConfiguration(client);
    
    // Step 4: Generate deployment report
    await generateDeploymentReport(client);
    
    log('success', 'Post-deployment verification completed successfully');
    log('success', 'System is ready for compliance operations');
    
  } catch (error) {
    log('error', `Post-deployment verification failed: ${error.message}`);
    process.exit(1);
  } finally {
    if (client) {
      await client.end();
      log('info', 'Database connection closed');
    }
  }
}

// Run post-deploy if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runPostDeploy().catch(error => {
    console.error('Post-deployment failed:', error);
    process.exit(1);
  });
}

export { runPostDeploy };
