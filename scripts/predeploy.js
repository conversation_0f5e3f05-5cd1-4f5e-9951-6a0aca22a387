#!/usr/bin/env node

/**
 * Pre-deployment Script for Compliance Command Center DBOS
 * This script handles pre-deployment tasks including mock data loading and verification
 */

import { Client } from 'pg';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const DATABASE_URL = process.env.DBOS_DATABASE_URL;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(level, message) {
  const timestamp = new Date().toISOString();
  const colorMap = {
    info: colors.blue,
    success: colors.green,
    warning: colors.yellow,
    error: colors.red
  };
  
  console.log(`${colorMap[level] || ''}[${timestamp}] [${level.toUpperCase()}] ${message}${colors.reset}`);
}

async function connectToDatabase() {
  if (!DATABASE_URL) {
    throw new Error('DBOS_DATABASE_URL environment variable is required');
  }

  const client = new Client({
    connectionString: DATABASE_URL,
    ssl: DATABASE_URL.includes('sslmode=require') ? { rejectUnauthorized: false } : false
  });

  try {
    await client.connect();
    log('info', 'Connected to database successfully');
    return client;
  } catch (error) {
    log('error', `Failed to connect to database: ${error.message}`);
    throw error;
  }
}

async function verifyDatabaseSetup(client) {
  log('info', 'Verifying database setup...');
  
  try {
    // Check if required tables exist
    const requiredTables = [
      'compliance_documents',
      'compliance_rules', 
      'kyc_profiles',
      'compliance_violations',
      'regulatory_updates',
      'compliance_reports',
      'workflow_executions',
      'audit_logs',
      'system_configuration',
      'notifications',
      'compliance_standards_config'
    ];
    
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      AND table_name = ANY($1)
    `, [requiredTables]);
    
    const existingTables = result.rows.map(row => row.table_name);
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));
    
    if (missingTables.length > 0) {
      throw new Error(`Missing required tables: ${missingTables.join(', ')}`);
    }
    
    log('success', `All ${requiredTables.length} required tables are present`);
    
    // Check if compliance rules are loaded
    const rulesResult = await client.query('SELECT COUNT(*) as count FROM compliance_rules WHERE is_active = true');
    const rulesCount = parseInt(rulesResult.rows[0].count);
    
    if (rulesCount === 0) {
      log('warning', 'No active compliance rules found - this may indicate incomplete data migration');
    } else {
      log('success', `Found ${rulesCount} active compliance rules`);
    }
    
    // Check if system configuration is loaded
    const configResult = await client.query('SELECT COUNT(*) as count FROM system_configuration WHERE is_active = true');
    const configCount = parseInt(configResult.rows[0].count);
    
    if (configCount === 0) {
      log('warning', 'No system configuration found - this may indicate incomplete data migration');
    } else {
      log('success', `Found ${configCount} system configuration settings`);
    }
    
    return true;
    
  } catch (error) {
    log('error', `Database verification failed: ${error.message}`);
    throw error;
  }
}

async function loadMockData(client) {
  if (NODE_ENV === 'production') {
    log('info', 'Skipping mock data loading in production environment');
    return;
  }
  
  log('info', 'Loading mock data for development/testing...');
  
  try {
    // Check if mock data already exists
    const docResult = await client.query('SELECT COUNT(*) as count FROM compliance_documents');
    const docCount = parseInt(docResult.rows[0].count);
    
    if (docCount > 0) {
      log('info', `Mock data already exists (${docCount} documents), skipping load`);
      return;
    }
    
    // Load mock compliance documents
    const mockDocuments = [
      {
        document_id: 'DOC-001',
        content: 'This quarterly earnings report shows revenue of $10M and net income of $2M. The company maintains strong financial controls and internal documentation as required by SOX compliance.',
        document_type: 'financial_report',
        file_name: 'Q1_2024_Earnings.pdf',
        uploaded_by: 'system'
      },
      {
        document_id: 'DOC-002', 
        content: 'Privacy policy update: We collect personal data including SSN and account numbers for identity verification. All customer financial information is encrypted and protected per GLBA requirements.',
        document_type: 'policy',
        file_name: 'Privacy_Policy_v2.pdf',
        uploaded_by: 'system'
      },
      {
        document_id: 'DOC-003',
        content: 'Consumer rights disclosure: California residents have the right to know what personal information we collect and the right to delete personal data. Data sale practices are disclosed.',
        document_type: 'policy', 
        file_name: 'CCPA_Disclosure.pdf',
        uploaded_by: 'system'
      }
    ];
    
    for (const doc of mockDocuments) {
      await client.query(`
        INSERT INTO compliance_documents (document_id, content, document_type, file_name, uploaded_by)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (document_id) DO NOTHING
      `, [doc.document_id, doc.content, doc.document_type, doc.file_name, doc.uploaded_by]);
    }
    
    // Load mock KYC profiles (with encrypted data)
    const mockKycProfiles = [
      {
        customer_id: 'CUST-001',
        name_encrypted: 'John Doe',
        date_of_birth_encrypted: '1985-06-15',
        ssn_encrypted: '***-**-1234',
        address_encrypted: '123 Main St, Anytown, ST 12345',
        risk_score: 25,
        status: 'approved'
      },
      {
        customer_id: 'CUST-002',
        name_encrypted: 'Jane Smith', 
        date_of_birth_encrypted: '1990-03-22',
        ssn_encrypted: '***-**-5678',
        address_encrypted: '456 Oak Ave, Somewhere, ST 67890',
        risk_score: 75,
        status: 'under_review'
      }
    ];
    
    for (const profile of mockKycProfiles) {
      await client.query(`
        INSERT INTO kyc_profiles (customer_id, name_encrypted, date_of_birth_encrypted, ssn_encrypted, address_encrypted, risk_score, status)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (customer_id) DO NOTHING
      `, [profile.customer_id, profile.name_encrypted, profile.date_of_birth_encrypted, 
          profile.ssn_encrypted, profile.address_encrypted, profile.risk_score, profile.status]);
    }
    
    log('success', `Loaded ${mockDocuments.length} mock documents and ${mockKycProfiles.length} mock KYC profiles`);
    
  } catch (error) {
    log('error', `Failed to load mock data: ${error.message}`);
    throw error;
  }
}

async function runPreDeploy() {
  let client;
  
  try {
    log('info', 'Starting pre-deployment tasks...');
    log('info', `Environment: ${NODE_ENV}`);
    
    client = await connectToDatabase();
    
    // Step 1: Verify database setup
    await verifyDatabaseSetup(client);
    
    // Step 2: Load mock data (if not production)
    await loadMockData(client);
    
    log('success', 'Pre-deployment tasks completed successfully');
    
  } catch (error) {
    log('error', `Pre-deployment failed: ${error.message}`);
    process.exit(1);
  } finally {
    if (client) {
      await client.end();
      log('info', 'Database connection closed');
    }
  }
}

// Run pre-deploy if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runPreDeploy().catch(error => {
    console.error('Pre-deployment failed:', error);
    process.exit(1);
  });
}

export { runPreDeploy };
