import { Router, Request, Response } from 'express';
import { DBOS } from "@dbos-inc/dbos-sdk";
import { ComplianceDatabase } from '../database';
import { ComplianceSystem } from '../workflows/compliance-system';

const router = Router();

// Regulatory impact analysis endpoint
router.post('/analyze-impact', async (req: Request, res: Response): Promise<void> => {
  try {
    const { regulatoryUpdates, documentIds, timeframe } = req.body;

    if (!regulatoryUpdates || regulatoryUpdates.length === 0) {
      res.status(400).json({ error: 'No regulatory updates specified for analysis' });
      return;
    }

    DBOS.logger.info(`Starting regulatory impact analysis for ${regulatoryUpdates.length} updates`);

    // Start the actual DBOS workflow for regulatory impact analysis
    const workflowHandle = await DBOS.startWorkflow(ComplianceSystem)
      .analyzeRegulatoryImpact({
        regulatoryUpdates,
        documentIds,
        timeframe
      });

    const workflowId = workflowHandle.workflowID;

    res.json({
      workflowId,
      status: 'analysis_started',
      message: `Regulatory impact analysis initiated for ${regulatoryUpdates.length} updates`
    });
  } catch (error) {
    DBOS.logger.error(`Error starting impact analysis: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get regulatory updates
router.get('/updates', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('📋 Regulatory updates requested');

    // Get real regulatory updates from database
    const updates = await ComplianceDatabase.getRegulatoryUpdates();

    // Transform database results to match frontend expectations
    const regulatoryUpdates = updates.map((update, index) => ({
      id: index + 1,
      source: update.standard,
      title: update.title,
      description: update.description,
      publishDate: update.effectiveDate.toISOString().split('T')[0],
      effectiveDate: update.effectiveDate.toISOString().split('T')[0],
      impact: update.impact.charAt(0).toUpperCase() + update.impact.slice(1),
      affectedDocuments: Math.floor(Math.random() * 30) + 5, // Mock for now
      status: update.actionRequired ? 'Action Required' : 'Monitoring',
      url: '#'
    }));

    res.json(regulatoryUpdates);
  } catch (error) {
    console.error('Error fetching regulatory updates:', error);
    res.status(500).json({ error: 'Failed to fetch regulatory updates' });
  }
});

// Get regulatory stats
router.get('/stats', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('📈 Regulatory stats requested');

    // Get real regulatory stats from database
    const regulatoryStats = await ComplianceDatabase.getRegulatoryStats();

    res.json(regulatoryStats);
  } catch (error) {
    console.error('Error fetching regulatory stats:', error);
    res.status(500).json({ error: 'Failed to fetch regulatory stats' });
  }
});

// Get regulatory sources
router.get('/sources', async (_req: Request, res: Response): Promise<void> => {
  try {
    console.log('🔍 Regulatory sources requested');

    // Get real regulatory sources from database
    const regulatorySources = await ComplianceDatabase.getRegulatorySourcesMonitoring();

    res.json(regulatorySources);
  } catch (error) {
    console.error('Error fetching regulatory sources:', error);
    res.status(500).json({ error: 'Failed to fetch regulatory sources' });
  }
});

export default router;
