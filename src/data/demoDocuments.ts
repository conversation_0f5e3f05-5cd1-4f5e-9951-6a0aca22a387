import { DemoDocument, DemoKYCProfile } from '../services/complianceApi';

// Demo documents for compliance checking
export const DEMO_DOCUMENTS: DemoDocument[] = [
  {
    id: 'demo-compliant-financial',
    name: 'Compliant Financial Report',
    description: 'A well-structured quarterly financial report that passes all compliance checks',
    documentType: 'financial_report',
    fileName: 'Q4_Financial_Report_Compliant.pdf',
    content: `Q4 2024 Financial Report

EXECUTIVE SUMMARY
This quarterly financial report provides a comprehensive overview of our financial performance for Q4 2024, prepared in accordance with SEC regulations and GAAP standards.

FINANCIAL HIGHLIGHTS
• Total Revenue: $12.5M (15% increase YoY)
• Net Income: $2.8M (12% increase YoY)
• Operating Cash Flow: $3.2M
• Total Assets: $45.6M
• Shareholders' Equity: $28.4M

REGULATORY COMPLIANCE
This report has been prepared in full compliance with:
- Securities and Exchange Commission (SEC) reporting requirements
- Sarbanes-Oxley Act (SOX) internal control provisions
- Generally Accepted Accounting Principles (GAAP)

All required disclosures have been included as per SEC Form 10-Q requirements.

RISK MANAGEMENT
We maintain comprehensive risk management procedures and internal controls as required by SOX Section 404.

FORWARD-LOOKING STATEMENTS
This report contains forward-looking statements as defined by the Private Securities Litigation Reform Act of 1995.`,
    expectedResult: {
      documentId: 'demo-compliant-financial',
      status: 'compliant',
      violations: [],
      complianceScore: 98.5
    }
  },
  {
    id: 'demo-non-compliant-contract',
    name: 'Non-Compliant Customer Contract',
    description: 'A customer contract with privacy and data protection violations',
    documentType: 'contract',
    fileName: 'Customer_Agreement_Non_Compliant.pdf',
    content: `CUSTOMER SERVICE AGREEMENT

TERMS AND CONDITIONS
This agreement governs the relationship between our company and the customer.

CUSTOMER DATA
We collect and store customer information including:
- Personal identification details
- Financial information
- Transaction history
- Browsing behavior and preferences

DATA USAGE
Customer data may be shared with third parties for marketing purposes. We reserve the right to use customer information for any business purpose.

PRIVACY NOTICE
Customer privacy is important to us. However, we cannot guarantee the security of personal information.

PAYMENT TERMS
All payments are due within 30 days. Late fees may apply.

TERMINATION
Either party may terminate this agreement with 30 days notice.

By signing below, customer agrees to all terms and conditions.`,
    expectedResult: {
      documentId: 'demo-non-compliant-contract',
      status: 'non_compliant',
      violations: [
        {
          documentId: 'demo-non-compliant-contract',
          ruleId: 'GLBA-001',
          violationType: 'privacy_disclosure',
          description: 'Missing required GLBA privacy notice and opt-out provisions',
          severity: 'high',
          recommendedAction: 'Add comprehensive privacy notice with customer opt-out rights',
          detectedAt: new Date()
        },
        {
          documentId: 'demo-non-compliant-contract',
          ruleId: 'CCPA-001',
          violationType: 'data_protection',
          description: 'Insufficient data protection disclosures for California residents',
          severity: 'medium',
          recommendedAction: 'Include CCPA-compliant data collection and usage disclosures',
          detectedAt: new Date()
        }
      ],
      complianceScore: 45.2
    }
  },
  {
    id: 'demo-review-policy',
    name: 'Policy Requiring Review',
    description: 'A data protection policy that needs manual review for compliance',
    documentType: 'policy',
    fileName: 'Data_Protection_Policy_Review.pdf',
    content: `DATA PROTECTION AND PRIVACY POLICY

PURPOSE
This policy establishes guidelines for the collection, use, and protection of personal data in compliance with applicable regulations.

SCOPE
This policy applies to all employees and contractors who handle personal data.

DATA COLLECTION
We collect personal data necessary for business operations, including:
- Customer contact information
- Financial data for transaction processing
- Employee records for HR purposes

DATA PROCESSING
Personal data is processed for legitimate business purposes. We implement appropriate technical and organizational measures to protect data.

DATA RETENTION
Personal data is retained for as long as necessary for business purposes or as required by law.

THIRD PARTY SHARING
We may share data with service providers under appropriate data processing agreements.

INDIVIDUAL RIGHTS
Individuals have rights regarding their personal data, including access, correction, and deletion rights.

SECURITY MEASURES
We implement industry-standard security measures to protect personal data from unauthorized access, disclosure, or destruction.

BREACH NOTIFICATION
In case of a data breach, we will notify relevant authorities and affected individuals as required by law.

CONTACT INFORMATION
For questions about this policy, contact our Data Protection Officer.`,
    expectedResult: {
      documentId: 'demo-review-policy',
      status: 'requires_review',
      violations: [
        {
          documentId: 'demo-review-policy',
          ruleId: 'GDPR-001',
          violationType: 'data_protection',
          description: 'Policy language may not fully comply with GDPR Article 13 transparency requirements',
          severity: 'medium',
          recommendedAction: 'Manual review required to ensure full GDPR compliance',
          detectedAt: new Date()
        }
      ],
      complianceScore: 78.3
    }
  }
];

// Demo KYC profiles for customer verification
export const DEMO_KYC_PROFILES: DemoKYCProfile[] = [
  {
    id: 'demo-approved-customer',
    name: 'Low-Risk Approved Customer',
    description: 'A standard customer profile that passes all KYC checks',
    personalInfo: {
      name: 'Sarah Johnson',
      dateOfBirth: '1985-03-15',
      ssn: '***-**-1234',
      address: '123 Main Street, Austin, TX 78701'
    },
    documents: {
      idDocument: 'drivers_license_tx.pdf',
      proofOfAddress: 'utility_bill_current.pdf',
      financialStatements: ['bank_statement_recent.pdf']
    },
    expectedResult: {
      customerId: 'demo-approved-customer',
      status: 'approved',
      riskScore: 15,
      reasons: [
        'Identity verification successful',
        'Address verification completed',
        'No sanctions list matches found',
        'Low risk profile based on provided information'
      ]
    }
  },
  {
    id: 'demo-rejected-customer',
    name: 'High-Risk Rejected Customer',
    description: 'A customer profile that fails KYC verification due to high risk factors',
    personalInfo: {
      name: 'John Smith',
      dateOfBirth: '1990-12-01',
      ssn: '***-**-5678',
      address: '456 Risk Avenue, Miami, FL 33101'
    },
    documents: {
      idDocument: 'passport_expired.pdf',
      proofOfAddress: 'lease_agreement_old.pdf'
    },
    expectedResult: {
      customerId: 'demo-rejected-customer',
      status: 'rejected',
      riskScore: 85,
      reasons: [
        'Expired identification document provided',
        'Address verification failed - outdated proof of address',
        'High-risk geographic location',
        'Insufficient financial documentation'
      ]
    }
  },
  {
    id: 'demo-review-customer',
    name: 'Customer Under Review',
    description: 'A customer profile requiring manual review due to moderate risk factors',
    personalInfo: {
      name: 'Maria Rodriguez',
      dateOfBirth: '1978-07-22',
      ssn: '***-**-9012',
      address: '789 Review Lane, New York, NY 10001'
    },
    documents: {
      idDocument: 'state_id_ny.pdf',
      proofOfAddress: 'bank_statement_address.pdf',
      financialStatements: ['tax_return_2023.pdf', 'employment_verification.pdf']
    },
    expectedResult: {
      customerId: 'demo-review-customer',
      status: 'under_review',
      riskScore: 55,
      reasons: [
        'Identity verification completed successfully',
        'Address verification requires manual review',
        'Moderate risk score requires compliance officer review',
        'Additional documentation may be needed'
      ]
    }
  }
];
