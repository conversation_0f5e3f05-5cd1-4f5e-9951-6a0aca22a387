{"/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 39}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 39}}, "3": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 17}}, "4": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 63}}, "5": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 90}}, "6": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 41}}, "7": {"start": {"line": 18, "column": 15}, "end": {"line": 18, "column": 39}}, "8": {"start": {"line": 19, "column": 2}, "end": {"line": 23, "column": 5}}, "9": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 89}}, "10": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 80}}, "11": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 68}}, "12": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 26}}}, "fnMap": {"0": {"name": "main", "decl": {"start": {"line": 9, "column": 15}, "end": {"line": 9, "column": 19}}, "loc": {"start": {"line": 9, "column": 19}, "end": {"line": 24, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 19, "column": 19}, "end": {"line": 19, "column": 22}}, "loc": {"start": {"line": 19, "column": 24}, "end": {"line": 23, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 15}, "end": {"line": 18, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 15}, "end": {"line": 18, "column": 31}}, {"start": {"line": 18, "column": 35}, "end": {"line": 18, "column": 39}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/vite-env.d.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/vite-env.d.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/components/ui/use-toast.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/components/ui/use-toast.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 9}}, "1": {"start": {"line": 3, "column": 9}, "end": {"line": 1, "column": 19}}, "2": {"start": {"line": 3, "column": 19}, "end": {"line": 1, "column": 52}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 17}}, "loc": {"start": {"line": 3, "column": 9}, "end": {"line": 1, "column": 19}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 3, "column": 19}, "end": {"line": 3, "column": 24}}, "loc": {"start": {"line": 3, "column": 19}, "end": {"line": 1, "column": 52}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/express.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/express.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 24}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 20}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 52}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 59}}, "5": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 29}}, "6": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 24}}, "7": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 24}}, "8": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 46}}, "9": {"start": {"line": 18, "column": 0}, "end": {"line": 40, "column": 3}}, "10": {"start": {"line": 19, "column": 22}, "end": {"line": 19, "column": 30}}, "11": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 53}}, "12": {"start": {"line": 21, "column": 2}, "end": {"line": 25, "column": 3}}, "13": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 38}}, "14": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 53}}, "15": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 11}}, "16": {"start": {"line": 28, "column": 21}, "end": {"line": 28, "column": 79}}, "17": {"start": {"line": 29, "column": 19}, "end": {"line": 29, "column": 65}}, "18": {"start": {"line": 32, "column": 2}, "end": {"line": 36, "column": 3}}, "19": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 36}}, "20": {"start": {"line": 34, "column": 9}, "end": {"line": 36, "column": 3}}, "21": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 34}}, "22": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 41}}, "23": {"start": {"line": 43, "column": 0}, "end": {"line": 49, "column": 3}}, "24": {"start": {"line": 44, "column": 2}, "end": {"line": 48, "column": 5}}, "25": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 18, "column": 71}, "end": {"line": 18, "column": 72}}, "loc": {"start": {"line": 18, "column": 90}, "end": {"line": 40, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 43, "column": 19}, "end": {"line": 43, "column": 20}}, "loc": {"start": {"line": 43, "column": 39}, "end": {"line": 49, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 25, "column": 3}}, "type": "if", "locations": [{"start": {"line": 21, "column": 2}, "end": {"line": 25, "column": 3}}]}, "1": {"loc": {"start": {"line": 32, "column": 2}, "end": {"line": 36, "column": 3}}, "type": "if", "locations": [{"start": {"line": 32, "column": 2}, "end": {"line": 36, "column": 3}}, {"start": {"line": 34, "column": 9}, "end": {"line": 36, "column": 3}}]}, "2": {"loc": {"start": {"line": 34, "column": 9}, "end": {"line": 36, "column": 3}}, "type": "if", "locations": [{"start": {"line": 34, "column": 9}, "end": {"line": 36, "column": 3}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0], "1": [0, 0], "2": [0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/index.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 26}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 25}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/queues.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/config/queues.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 51}}, "1": {"start": {"line": 4, "column": 13}, "end": {"line": 7, "column": 3}}, "2": {"start": {"line": 9, "column": 13}, "end": {"line": 12, "column": 3}}, "3": {"start": {"line": 14, "column": 13}, "end": {"line": 16, "column": 3}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/database/compliance-database.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/database/compliance-database.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 16, "column": 19}, "end": {"line": 21, "column": 6}}, "2": {"start": {"line": 23, "column": 4}, "end": {"line": 30, "column": 8}}, "3": {"start": {"line": 23, "column": 42}, "end": {"line": 30, "column": 6}}, "4": {"start": {"line": 35, "column": 19}, "end": {"line": 44, "column": 40}}, "5": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 29}}, "6": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 40}}, "7": {"start": {"line": 51, "column": 33}, "end": {"line": 51, "column": 40}}, "8": {"start": {"line": 54, "column": 24}, "end": {"line": 54, "column": 71}}, "9": {"start": {"line": 54, "column": 56}, "end": {"line": 54, "column": 68}}, "10": {"start": {"line": 55, "column": 28}, "end": {"line": 55, "column": 53}}, "11": {"start": {"line": 57, "column": 4}, "end": {"line": 65, "column": 5}}, "12": {"start": {"line": 58, "column": 21}, "end": {"line": 60, "column": null}}, "13": {"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}, "14": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 54}}, "15": {"start": {"line": 68, "column": 20}, "end": {"line": 68, "column": 63}}, "16": {"start": {"line": 68, "column": 52}, "end": {"line": 68, "column": 60}}, "17": {"start": {"line": 69, "column": 24}, "end": {"line": 69, "column": 49}}, "18": {"start": {"line": 71, "column": 4}, "end": {"line": 79, "column": 5}}, "19": {"start": {"line": 72, "column": 21}, "end": {"line": 74, "column": null}}, "20": {"start": {"line": 76, "column": 6}, "end": {"line": 78, "column": 7}}, "21": {"start": {"line": 77, "column": 8}, "end": {"line": 77, "column": 51}}, "22": {"start": {"line": 82, "column": 28}, "end": {"line": 83, "column": null}}, "23": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 68}}, "24": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 45}}, "25": {"start": {"line": 86, "column": 38}, "end": {"line": 86, "column": 45}}, "26": {"start": {"line": 88, "column": 19}, "end": {"line": 90, "column": 16}}, "27": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 122}}, "28": {"start": {"line": 92, "column": 19}, "end": {"line": 94, "column": 6}}, "29": {"start": {"line": 92, "column": 48}, "end": {"line": 94, "column": 6}}, "30": {"start": {"line": 96, "column": 4}, "end": {"line": 99, "column": 15}}, "31": {"start": {"line": 104, "column": 19}, "end": {"line": 132, "column": 6}}, "32": {"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 29}}, "33": {"start": {"line": 144, "column": 16}, "end": {"line": 156, "column": 6}}, "34": {"start": {"line": 158, "column": 26}, "end": {"line": 158, "column": 28}}, "35": {"start": {"line": 159, "column": 4}, "end": {"line": 161, "column": 5}}, "36": {"start": {"line": 160, "column": 6}, "end": {"line": 160, "column": 29}}, "37": {"start": {"line": 163, "column": 26}, "end": {"line": 163, "column": 66}}, "38": {"start": {"line": 165, "column": 16}, "end": {"line": 165, "column": 37}}, "39": {"start": {"line": 166, "column": 27}, "end": {"line": 166, "column": 61}}, "40": {"start": {"line": 167, "column": 31}, "end": {"line": 167, "column": 69}}, "41": {"start": {"line": 168, "column": 28}, "end": {"line": 168, "column": 63}}, "42": {"start": {"line": 169, "column": 27}, "end": {"line": 169, "column": 97}}, "43": {"start": {"line": 171, "column": 4}, "end": {"line": 176, "column": 6}}, "44": {"start": {"line": 181, "column": 19}, "end": {"line": 185, "column": 110}}, "45": {"start": {"line": 187, "column": 4}, "end": {"line": 187, "column": 29}}, "46": {"start": {"line": 192, "column": 19}, "end": {"line": 199, "column": 6}}, "47": {"start": {"line": 201, "column": 4}, "end": {"line": 209, "column": 8}}, "48": {"start": {"line": 201, "column": 42}, "end": {"line": 209, "column": 6}}, "49": {"start": {"line": 220, "column": 26}, "end": {"line": 229, "column": 6}}, "50": {"start": {"line": 231, "column": 16}, "end": {"line": 231, "column": 37}}, "51": {"start": {"line": 232, "column": 4}, "end": {"line": 238, "column": 6}}, "52": {"start": {"line": 243, "column": 19}, "end": {"line": 279, "column": 6}}, "53": {"start": {"line": 281, "column": 4}, "end": {"line": 287, "column": 8}}, "54": {"start": {"line": 281, "column": 42}, "end": {"line": 287, "column": 6}}, "55": {"start": {"line": 297, "column": 19}, "end": {"line": 310, "column": 6}}, "56": {"start": {"line": 312, "column": 16}, "end": {"line": 312, "column": 30}}, "57": {"start": {"line": 313, "column": 29}, "end": {"line": 313, "column": 65}}, "58": {"start": {"line": 314, "column": 31}, "end": {"line": 314, "column": 69}}, "59": {"start": {"line": 315, "column": 33}, "end": {"line": 315, "column": 78}}, "60": {"start": {"line": 317, "column": 4}, "end": {"line": 323, "column": 6}}, "61": {"start": {"line": 333, "column": 19}, "end": {"line": 356, "column": 6}}, "62": {"start": {"line": 358, "column": 16}, "end": {"line": 358, "column": 30}}, "63": {"start": {"line": 359, "column": 33}, "end": {"line": 359, "column": 87}}, "64": {"start": {"line": 360, "column": 30}, "end": {"line": 360, "column": 69}}, "65": {"start": {"line": 361, "column": 32}, "end": {"line": 361, "column": 71}}, "66": {"start": {"line": 362, "column": 27}, "end": {"line": 362, "column": 61}}, "67": {"start": {"line": 363, "column": 19}, "end": {"line": 363, "column": 91}}, "68": {"start": {"line": 365, "column": 4}, "end": {"line": 370, "column": 6}}, "69": {"start": {"line": 380, "column": 19}, "end": {"line": 409, "column": 6}}, "70": {"start": {"line": 411, "column": 16}, "end": {"line": 411, "column": 30}}, "71": {"start": {"line": 412, "column": 21}, "end": {"line": 412, "column": 23}}, "72": {"start": {"line": 415, "column": 4}, "end": {"line": 422, "column": 5}}, "73": {"start": {"line": 416, "column": 6}, "end": {"line": 421, "column": 9}}, "74": {"start": {"line": 425, "column": 24}, "end": {"line": 425, "column": 68}}, "75": {"start": {"line": 426, "column": 25}, "end": {"line": 426, "column": 70}}, "76": {"start": {"line": 427, "column": 4}, "end": {"line": 435, "column": 5}}, "77": {"start": {"line": 428, "column": 26}, "end": {"line": 428, "column": 76}}, "78": {"start": {"line": 429, "column": 6}, "end": {"line": 434, "column": 9}}, "79": {"start": {"line": 438, "column": 25}, "end": {"line": 438, "column": 63}}, "80": {"start": {"line": 439, "column": 4}, "end": {"line": 446, "column": 5}}, "81": {"start": {"line": 440, "column": 6}, "end": {"line": 445, "column": 9}}, "82": {"start": {"line": 449, "column": 4}, "end": {"line": 470, "column": 5}}, "83": {"start": {"line": 450, "column": 6}, "end": {"line": 469, "column": 8}}, "84": {"start": {"line": 472, "column": 4}, "end": {"line": 472, "column": 20}}, "85": {"start": {"line": 477, "column": 19}, "end": {"line": 502, "column": 6}}, "86": {"start": {"line": 504, "column": 4}, "end": {"line": 525, "column": 8}}, "87": {"start": {"line": 504, "column": 57}, "end": {"line": 525, "column": 6}}, "88": {"start": {"line": 530, "column": 19}, "end": {"line": 565, "column": 6}}, "89": {"start": {"line": 567, "column": 4}, "end": {"line": 576, "column": 8}}, "90": {"start": {"line": 567, "column": 42}, "end": {"line": 576, "column": 6}}, "91": {"start": {"line": 581, "column": 19}, "end": {"line": 616, "column": 6}}, "92": {"start": {"line": 618, "column": 4}, "end": {"line": 644, "column": 7}}, "93": {"start": {"line": 619, "column": 29}, "end": {"line": 619, "column": 65}}, "94": {"start": {"line": 620, "column": 32}, "end": {"line": 620, "column": 35}}, "95": {"start": {"line": 621, "column": 23}, "end": {"line": 623, "column": 41}}, "96": {"start": {"line": 625, "column": 6}, "end": {"line": 643, "column": 8}}, "97": {"start": {"line": 649, "column": 19}, "end": {"line": 668, "column": 6}}, "98": {"start": {"line": 670, "column": 4}, "end": {"line": 678, "column": 8}}, "99": {"start": {"line": 670, "column": 42}, "end": {"line": 678, "column": 6}}, "100": {"start": {"line": 683, "column": 19}, "end": {"line": 700, "column": 6}}, "101": {"start": {"line": 702, "column": 4}, "end": {"line": 709, "column": 8}}, "102": {"start": {"line": 702, "column": 57}, "end": {"line": 709, "column": 6}}, "103": {"start": {"line": 719, "column": 19}, "end": {"line": 733, "column": 6}}, "104": {"start": {"line": 735, "column": 16}, "end": {"line": 735, "column": 30}}, "105": {"start": {"line": 736, "column": 27}, "end": {"line": 736, "column": 64}}, "106": {"start": {"line": 737, "column": 20}, "end": {"line": 737, "column": 62}}, "107": {"start": {"line": 738, "column": 27}, "end": {"line": 738, "column": 62}}, "108": {"start": {"line": 739, "column": 26}, "end": {"line": 739, "column": 60}}, "109": {"start": {"line": 741, "column": 4}, "end": {"line": 746, "column": 6}}, "110": {"start": {"line": 756, "column": 19}, "end": {"line": 762, "column": 6}}, "111": {"start": {"line": 764, "column": 16}, "end": {"line": 764, "column": 30}}, "112": {"start": {"line": 765, "column": 25}, "end": {"line": 765, "column": 58}}, "113": {"start": {"line": 766, "column": 29}, "end": {"line": 766, "column": 65}}, "114": {"start": {"line": 767, "column": 28}, "end": {"line": 767, "column": 64}}, "115": {"start": {"line": 768, "column": 30}, "end": {"line": 768, "column": 68}}, "116": {"start": {"line": 770, "column": 4}, "end": {"line": 775, "column": 6}}, "117": {"start": {"line": 785, "column": 19}, "end": {"line": 800, "column": 6}}, "118": {"start": {"line": 803, "column": 4}, "end": {"line": 810, "column": 5}}, "119": {"start": {"line": 804, "column": 6}, "end": {"line": 809, "column": 8}}, "120": {"start": {"line": 812, "column": 4}, "end": {"line": 817, "column": 8}}, "121": {"start": {"line": 812, "column": 42}, "end": {"line": 817, "column": 6}}, "122": {"start": {"line": 827, "column": 19}, "end": {"line": 841, "column": 6}}, "123": {"start": {"line": 843, "column": 16}, "end": {"line": 843, "column": 30}}, "124": {"start": {"line": 844, "column": 28}, "end": {"line": 844, "column": 66}}, "125": {"start": {"line": 845, "column": 24}, "end": {"line": 845, "column": 60}}, "126": {"start": {"line": 846, "column": 23}, "end": {"line": 846, "column": 68}}, "127": {"start": {"line": 847, "column": 28}, "end": {"line": 847, "column": 64}}, "128": {"start": {"line": 849, "column": 4}, "end": {"line": 854, "column": 6}}, "129": {"start": {"line": 863, "column": 19}, "end": {"line": 875, "column": 6}}, "130": {"start": {"line": 877, "column": 16}, "end": {"line": 877, "column": 30}}, "131": {"start": {"line": 878, "column": 27}, "end": {"line": 878, "column": 63}}, "132": {"start": {"line": 879, "column": 31}, "end": {"line": 879, "column": 71}}, "133": {"start": {"line": 880, "column": 28}, "end": {"line": 880, "column": 63}}, "134": {"start": {"line": 881, "column": 23}, "end": {"line": 881, "column": 68}}, "135": {"start": {"line": 882, "column": 28}, "end": {"line": 882, "column": 63}}, "136": {"start": {"line": 884, "column": 24}, "end": {"line": 884, "column": 111}}, "137": {"start": {"line": 885, "column": 23}, "end": {"line": 885, "column": 121}}, "138": {"start": {"line": 887, "column": 4}, "end": {"line": 913, "column": 6}}, "139": {"start": {"line": 924, "column": 19}, "end": {"line": 931, "column": 6}}, "140": {"start": {"line": 933, "column": 16}, "end": {"line": 933, "column": 30}}, "141": {"start": {"line": 934, "column": 4}, "end": {"line": 940, "column": 6}}, "142": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 13}}, "143": {"start": {"line": 15, "column": 15}, "end": {"line": 31, "column": null}}, "144": {"start": {"line": 34, "column": 15}, "end": {"line": 47, "column": null}}, "145": {"start": {"line": 50, "column": 15}, "end": {"line": 100, "column": null}}, "146": {"start": {"line": 103, "column": 15}, "end": {"line": 135, "column": null}}, "147": {"start": {"line": 138, "column": 15}, "end": {"line": 177, "column": null}}, "148": {"start": {"line": 180, "column": 15}, "end": {"line": 188, "column": null}}, "149": {"start": {"line": 191, "column": 15}, "end": {"line": 210, "column": null}}, "150": {"start": {"line": 213, "column": 15}, "end": {"line": 239, "column": null}}, "151": {"start": {"line": 242, "column": 15}, "end": {"line": 288, "column": null}}, "152": {"start": {"line": 291, "column": 15}, "end": {"line": 324, "column": null}}, "153": {"start": {"line": 327, "column": 15}, "end": {"line": 371, "column": null}}, "154": {"start": {"line": 374, "column": 15}, "end": {"line": 473, "column": null}}, "155": {"start": {"line": 476, "column": 15}, "end": {"line": 526, "column": null}}, "156": {"start": {"line": 529, "column": 15}, "end": {"line": 577, "column": null}}, "157": {"start": {"line": 580, "column": 15}, "end": {"line": 645, "column": null}}, "158": {"start": {"line": 648, "column": 15}, "end": {"line": 679, "column": null}}, "159": {"start": {"line": 682, "column": 15}, "end": {"line": 710, "column": null}}, "160": {"start": {"line": 713, "column": 15}, "end": {"line": 747, "column": null}}, "161": {"start": {"line": 750, "column": 15}, "end": {"line": 776, "column": null}}, "162": {"start": {"line": 779, "column": 15}, "end": {"line": 818, "column": null}}, "163": {"start": {"line": 821, "column": 15}, "end": {"line": 855, "column": null}}, "164": {"start": {"line": 858, "column": 15}, "end": {"line": 914, "column": null}}, "165": {"start": {"line": 917, "column": 15}, "end": {"line": 941, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 8}}, "loc": {"start": {"line": 15, "column": 39}, "end": {"line": 31, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 23, "column": 27}, "end": {"line": 23, "column": 28}}, "loc": {"start": {"line": 23, "column": 42}, "end": {"line": 30, "column": 6}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 8}}, "loc": {"start": {"line": 34, "column": 56}, "end": {"line": 47, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 8}}, "loc": {"start": {"line": 50, "column": 63}, "end": {"line": 100, "column": 3}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 54, "column": 51}, "end": {"line": 54, "column": 52}}, "loc": {"start": {"line": 54, "column": 56}, "end": {"line": 54, "column": 68}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 68, "column": 47}, "end": {"line": 68, "column": 48}}, "loc": {"start": {"line": 68, "column": 52}, "end": {"line": 68, "column": 60}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 82, "column": 46}, "end": {"line": 82, "column": 47}}, "loc": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 68}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 88, "column": 39}, "end": {"line": 88, "column": 40}}, "loc": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 122}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 92, "column": 43}, "end": {"line": 92, "column": 44}}, "loc": {"start": {"line": 92, "column": 48}, "end": {"line": 94, "column": 6}}}, "9": {"name": "(anonymous_11)", "decl": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 8}}, "loc": {"start": {"line": 103, "column": 49}, "end": {"line": 135, "column": 3}}}, "10": {"name": "(anonymous_12)", "decl": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": 8}}, "loc": {"start": {"line": 138, "column": 56}, "end": {"line": 177, "column": 3}}}, "11": {"name": "(anonymous_13)", "decl": {"start": {"line": 180, "column": 2}, "end": {"line": 180, "column": 8}}, "loc": {"start": {"line": 180, "column": 60}, "end": {"line": 188, "column": 3}}}, "12": {"name": "(anonymous_14)", "decl": {"start": {"line": 191, "column": 2}, "end": {"line": 191, "column": 8}}, "loc": {"start": {"line": 191, "column": 35}, "end": {"line": 210, "column": 3}}}, "13": {"name": "(anonymous_15)", "decl": {"start": {"line": 201, "column": 27}, "end": {"line": 201, "column": 28}}, "loc": {"start": {"line": 201, "column": 42}, "end": {"line": 209, "column": 6}}}, "14": {"name": "(anonymous_16)", "decl": {"start": {"line": 213, "column": 2}, "end": {"line": 213, "column": 8}}, "loc": {"start": {"line": 213, "column": 34}, "end": {"line": 239, "column": 3}}}, "15": {"name": "(anonymous_17)", "decl": {"start": {"line": 242, "column": 2}, "end": {"line": 242, "column": 8}}, "loc": {"start": {"line": 242, "column": 37}, "end": {"line": 288, "column": 3}}}, "16": {"name": "(anonymous_18)", "decl": {"start": {"line": 281, "column": 27}, "end": {"line": 281, "column": 28}}, "loc": {"start": {"line": 281, "column": 42}, "end": {"line": 287, "column": 6}}}, "17": {"name": "(anonymous_19)", "decl": {"start": {"line": 291, "column": 2}, "end": {"line": 291, "column": 8}}, "loc": {"start": {"line": 291, "column": 31}, "end": {"line": 324, "column": 3}}}, "18": {"name": "(anonymous_20)", "decl": {"start": {"line": 327, "column": 2}, "end": {"line": 327, "column": 8}}, "loc": {"start": {"line": 327, "column": 37}, "end": {"line": 371, "column": 3}}}, "19": {"name": "(anonymous_21)", "decl": {"start": {"line": 374, "column": 2}, "end": {"line": 374, "column": 8}}, "loc": {"start": {"line": 374, "column": 28}, "end": {"line": 473, "column": 3}}}, "20": {"name": "(anonymous_22)", "decl": {"start": {"line": 476, "column": 2}, "end": {"line": 476, "column": 8}}, "loc": {"start": {"line": 476, "column": 26}, "end": {"line": 526, "column": 3}}}, "21": {"name": "(anonymous_23)", "decl": {"start": {"line": 504, "column": 27}, "end": {"line": 504, "column": 28}}, "loc": {"start": {"line": 504, "column": 57}, "end": {"line": 525, "column": 6}}}, "22": {"name": "(anonymous_24)", "decl": {"start": {"line": 529, "column": 2}, "end": {"line": 529, "column": 8}}, "loc": {"start": {"line": 529, "column": 31}, "end": {"line": 577, "column": 3}}}, "23": {"name": "(anonymous_25)", "decl": {"start": {"line": 567, "column": 27}, "end": {"line": 567, "column": 28}}, "loc": {"start": {"line": 567, "column": 42}, "end": {"line": 576, "column": 6}}}, "24": {"name": "(anonymous_26)", "decl": {"start": {"line": 580, "column": 2}, "end": {"line": 580, "column": 8}}, "loc": {"start": {"line": 580, "column": 33}, "end": {"line": 645, "column": 3}}}, "25": {"name": "(anonymous_27)", "decl": {"start": {"line": 618, "column": 27}, "end": {"line": 618, "column": 28}}, "loc": {"start": {"line": 618, "column": 55}, "end": {"line": 644, "column": 5}}}, "26": {"name": "(anonymous_28)", "decl": {"start": {"line": 648, "column": 2}, "end": {"line": 648, "column": 8}}, "loc": {"start": {"line": 648, "column": 33}, "end": {"line": 679, "column": 3}}}, "27": {"name": "(anonymous_29)", "decl": {"start": {"line": 670, "column": 27}, "end": {"line": 670, "column": 28}}, "loc": {"start": {"line": 670, "column": 42}, "end": {"line": 678, "column": 6}}}, "28": {"name": "(anonymous_30)", "decl": {"start": {"line": 682, "column": 2}, "end": {"line": 682, "column": 8}}, "loc": {"start": {"line": 682, "column": 34}, "end": {"line": 710, "column": 3}}}, "29": {"name": "(anonymous_31)", "decl": {"start": {"line": 702, "column": 27}, "end": {"line": 702, "column": 28}}, "loc": {"start": {"line": 702, "column": 57}, "end": {"line": 709, "column": 6}}}, "30": {"name": "(anonymous_32)", "decl": {"start": {"line": 713, "column": 2}, "end": {"line": 713, "column": 8}}, "loc": {"start": {"line": 713, "column": 26}, "end": {"line": 747, "column": 3}}}, "31": {"name": "(anonymous_33)", "decl": {"start": {"line": 750, "column": 2}, "end": {"line": 750, "column": 8}}, "loc": {"start": {"line": 750, "column": 33}, "end": {"line": 776, "column": 3}}}, "32": {"name": "(anonymous_34)", "decl": {"start": {"line": 779, "column": 2}, "end": {"line": 779, "column": 8}}, "loc": {"start": {"line": 779, "column": 45}, "end": {"line": 818, "column": 3}}}, "33": {"name": "(anonymous_35)", "decl": {"start": {"line": 812, "column": 27}, "end": {"line": 812, "column": 28}}, "loc": {"start": {"line": 812, "column": 42}, "end": {"line": 817, "column": 6}}}, "34": {"name": "(anonymous_36)", "decl": {"start": {"line": 821, "column": 2}, "end": {"line": 821, "column": 8}}, "loc": {"start": {"line": 821, "column": 31}, "end": {"line": 855, "column": 3}}}, "35": {"name": "(anonymous_37)", "decl": {"start": {"line": 858, "column": 2}, "end": {"line": 858, "column": 8}}, "loc": {"start": {"line": 858, "column": 33}, "end": {"line": 914, "column": 3}}}, "36": {"name": "(anonymous_38)", "decl": {"start": {"line": 917, "column": 2}, "end": {"line": 917, "column": 8}}, "loc": {"start": {"line": 917, "column": 29}, "end": {"line": 941, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 40}}, "type": "if", "locations": [{"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 40}}]}, "1": {"loc": {"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}, "type": "if", "locations": [{"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}]}, "2": {"loc": {"start": {"line": 76, "column": 6}, "end": {"line": 78, "column": 7}}, "type": "if", "locations": [{"start": {"line": 76, "column": 6}, "end": {"line": 78, "column": 7}}]}, "3": {"loc": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 39}}, {"start": {"line": 83, "column": 43}, "end": {"line": 83, "column": 68}}]}, "4": {"loc": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 45}}, "type": "if", "locations": [{"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 45}}]}, "5": {"loc": {"start": {"line": 152, "column": 11}, "end": {"line": 152, "column": 78}}, "type": "cond-expr", "locations": [{"start": {"line": 152, "column": 47}, "end": {"line": 152, "column": 73}}, {"start": {"line": 152, "column": 76}, "end": {"line": 152, "column": 78}}]}, "6": {"loc": {"start": {"line": 152, "column": 11}, "end": {"line": 152, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 152, "column": 11}, "end": {"line": 152, "column": 20}}, {"start": {"line": 152, "column": 24}, "end": {"line": 152, "column": 44}}]}, "7": {"loc": {"start": {"line": 159, "column": 4}, "end": {"line": 161, "column": 5}}, "type": "if", "locations": [{"start": {"line": 159, "column": 4}, "end": {"line": 161, "column": 5}}]}, "8": {"loc": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 17}}, {"start": {"line": 159, "column": 21}, "end": {"line": 159, "column": 41}}]}, "9": {"loc": {"start": {"line": 166, "column": 27}, "end": {"line": 166, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 166, "column": 27}, "end": {"line": 166, "column": 56}}, {"start": {"line": 166, "column": 60}, "end": {"line": 166, "column": 61}}]}, "10": {"loc": {"start": {"line": 167, "column": 31}, "end": {"line": 167, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 167, "column": 31}, "end": {"line": 167, "column": 64}}, {"start": {"line": 167, "column": 68}, "end": {"line": 167, "column": 69}}]}, "11": {"loc": {"start": {"line": 168, "column": 28}, "end": {"line": 168, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 168, "column": 28}, "end": {"line": 168, "column": 58}}, {"start": {"line": 168, "column": 62}, "end": {"line": 168, "column": 63}}]}, "12": {"loc": {"start": {"line": 169, "column": 27}, "end": {"line": 169, "column": 97}}, "type": "cond-expr", "locations": [{"start": {"line": 169, "column": 48}, "end": {"line": 169, "column": 91}}, {"start": {"line": 169, "column": 94}, "end": {"line": 169, "column": 97}}]}, "13": {"loc": {"start": {"line": 233, "column": 22}, "end": {"line": 233, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 233, "column": 22}, "end": {"line": 233, "column": 53}}, {"start": {"line": 233, "column": 57}, "end": {"line": 233, "column": 61}}]}, "14": {"loc": {"start": {"line": 234, "column": 24}, "end": {"line": 234, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 234, "column": 24}, "end": {"line": 234, "column": 55}}, {"start": {"line": 234, "column": 59}, "end": {"line": 234, "column": 60}}]}, "15": {"loc": {"start": {"line": 235, "column": 18}, "end": {"line": 235, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 235, "column": 18}, "end": {"line": 235, "column": 43}}, {"start": {"line": 235, "column": 47}, "end": {"line": 235, "column": 48}}]}, "16": {"loc": {"start": {"line": 236, "column": 24}, "end": {"line": 236, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 236, "column": 24}, "end": {"line": 236, "column": 55}}, {"start": {"line": 236, "column": 59}, "end": {"line": 236, "column": 60}}]}, "17": {"loc": {"start": {"line": 237, "column": 25}, "end": {"line": 237, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 237, "column": 25}, "end": {"line": 237, "column": 57}}, {"start": {"line": 237, "column": 61}, "end": {"line": 237, "column": 62}}]}, "18": {"loc": {"start": {"line": 282, "column": 12}, "end": {"line": 282, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 282, "column": 12}, "end": {"line": 282, "column": 28}}, {"start": {"line": 282, "column": 32}, "end": {"line": 282, "column": 40}}]}, "19": {"loc": {"start": {"line": 285, "column": 17}, "end": {"line": 285, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 285, "column": 17}, "end": {"line": 285, "column": 30}}, {"start": {"line": 285, "column": 34}, "end": {"line": 285, "column": 72}}]}, "20": {"loc": {"start": {"line": 313, "column": 29}, "end": {"line": 313, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 313, "column": 29}, "end": {"line": 313, "column": 60}}, {"start": {"line": 313, "column": 64}, "end": {"line": 313, "column": 65}}]}, "21": {"loc": {"start": {"line": 314, "column": 31}, "end": {"line": 314, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 314, "column": 31}, "end": {"line": 314, "column": 64}}, {"start": {"line": 314, "column": 68}, "end": {"line": 314, "column": 69}}]}, "22": {"loc": {"start": {"line": 315, "column": 33}, "end": {"line": 315, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 315, "column": 33}, "end": {"line": 315, "column": 71}}, {"start": {"line": 315, "column": 75}, "end": {"line": 315, "column": 78}}]}, "23": {"loc": {"start": {"line": 321, "column": 21}, "end": {"line": 322, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 322, "column": 8}, "end": {"line": 322, "column": 75}}, {"start": {"line": 322, "column": 78}, "end": {"line": 322, "column": 79}}]}, "24": {"loc": {"start": {"line": 359, "column": 33}, "end": {"line": 359, "column": 87}}, "type": "binary-expr", "locations": [{"start": {"line": 359, "column": 33}, "end": {"line": 359, "column": 80}}, {"start": {"line": 359, "column": 84}, "end": {"line": 359, "column": 87}}]}, "25": {"loc": {"start": {"line": 360, "column": 30}, "end": {"line": 360, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 360, "column": 30}, "end": {"line": 360, "column": 63}}, {"start": {"line": 360, "column": 67}, "end": {"line": 360, "column": 69}}]}, "26": {"loc": {"start": {"line": 361, "column": 32}, "end": {"line": 361, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 361, "column": 32}, "end": {"line": 361, "column": 66}}, {"start": {"line": 361, "column": 70}, "end": {"line": 361, "column": 71}}]}, "27": {"loc": {"start": {"line": 362, "column": 27}, "end": {"line": 362, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 362, "column": 27}, "end": {"line": 362, "column": 56}}, {"start": {"line": 362, "column": 60}, "end": {"line": 362, "column": 61}}]}, "28": {"loc": {"start": {"line": 363, "column": 19}, "end": {"line": 363, "column": 91}}, "type": "cond-expr", "locations": [{"start": {"line": 363, "column": 41}, "end": {"line": 363, "column": 83}}, {"start": {"line": 363, "column": 87}, "end": {"line": 363, "column": 91}}]}, "29": {"loc": {"start": {"line": 415, "column": 4}, "end": {"line": 422, "column": 5}}, "type": "if", "locations": [{"start": {"line": 415, "column": 4}, "end": {"line": 422, "column": 5}}]}, "30": {"loc": {"start": {"line": 415, "column": 8}, "end": {"line": 415, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 415, "column": 8}, "end": {"line": 415, "column": 30}}, {"start": {"line": 415, "column": 34}, "end": {"line": 415, "column": 61}}]}, "31": {"loc": {"start": {"line": 425, "column": 24}, "end": {"line": 425, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 425, "column": 24}, "end": {"line": 425, "column": 63}}, {"start": {"line": 425, "column": 67}, "end": {"line": 425, "column": 68}}]}, "32": {"loc": {"start": {"line": 426, "column": 25}, "end": {"line": 426, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 426, "column": 25}, "end": {"line": 426, "column": 65}}, {"start": {"line": 426, "column": 69}, "end": {"line": 426, "column": 70}}]}, "33": {"loc": {"start": {"line": 427, "column": 4}, "end": {"line": 435, "column": 5}}, "type": "if", "locations": [{"start": {"line": 427, "column": 4}, "end": {"line": 435, "column": 5}}]}, "34": {"loc": {"start": {"line": 438, "column": 25}, "end": {"line": 438, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 438, "column": 25}, "end": {"line": 438, "column": 58}}, {"start": {"line": 438, "column": 62}, "end": {"line": 438, "column": 63}}]}, "35": {"loc": {"start": {"line": 439, "column": 4}, "end": {"line": 446, "column": 5}}, "type": "if", "locations": [{"start": {"line": 439, "column": 4}, "end": {"line": 446, "column": 5}}]}, "36": {"loc": {"start": {"line": 449, "column": 4}, "end": {"line": 470, "column": 5}}, "type": "if", "locations": [{"start": {"line": 449, "column": 4}, "end": {"line": 470, "column": 5}}]}, "37": {"loc": {"start": {"line": 514, "column": 14}, "end": {"line": 517, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 514, "column": 41}, "end": {"line": 514, "column": 64}}, {"start": {"line": 515, "column": 14}, "end": {"line": 517, "column": 67}}]}, "38": {"loc": {"start": {"line": 515, "column": 14}, "end": {"line": 517, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 515, "column": 46}, "end": {"line": 515, "column": 70}}, {"start": {"line": 516, "column": 14}, "end": {"line": 517, "column": 67}}]}, "39": {"loc": {"start": {"line": 516, "column": 14}, "end": {"line": 517, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 516, "column": 42}, "end": {"line": 516, "column": 53}}, {"start": {"line": 517, "column": 14}, "end": {"line": 517, "column": 67}}]}, "40": {"loc": {"start": {"line": 517, "column": 14}, "end": {"line": 517, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 517, "column": 42}, "end": {"line": 517, "column": 52}}, {"start": {"line": 517, "column": 55}, "end": {"line": 517, "column": 67}}]}, "41": {"loc": {"start": {"line": 518, "column": 17}, "end": {"line": 518, "column": 86}}, "type": "cond-expr", "locations": [{"start": {"line": 518, "column": 39}, "end": {"line": 518, "column": 44}}, {"start": {"line": 518, "column": 47}, "end": {"line": 518, "column": 86}}]}, "42": {"loc": {"start": {"line": 518, "column": 47}, "end": {"line": 518, "column": 86}}, "type": "cond-expr", "locations": [{"start": {"line": 518, "column": 69}, "end": {"line": 518, "column": 77}}, {"start": {"line": 518, "column": 80}, "end": {"line": 518, "column": 86}}]}, "43": {"loc": {"start": {"line": 519, "column": 21}, "end": {"line": 521, "column": 36}}, "type": "cond-expr", "locations": [{"start": {"line": 519, "column": 78}, "end": {"line": 519, "column": 89}}, {"start": {"line": 520, "column": 21}, "end": {"line": 521, "column": 36}}]}, "44": {"loc": {"start": {"line": 519, "column": 21}, "end": {"line": 519, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 519, "column": 21}, "end": {"line": 519, "column": 46}}, {"start": {"line": 519, "column": 50}, "end": {"line": 519, "column": 75}}]}, "45": {"loc": {"start": {"line": 520, "column": 21}, "end": {"line": 521, "column": 36}}, "type": "cond-expr", "locations": [{"start": {"line": 520, "column": 53}, "end": {"line": 520, "column": 68}}, {"start": {"line": 521, "column": 21}, "end": {"line": 521, "column": 36}}]}, "46": {"loc": {"start": {"line": 524, "column": 13}, "end": {"line": 524, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 524, "column": 13}, "end": {"line": 524, "column": 22}}, {"start": {"line": 524, "column": 26}, "end": {"line": 524, "column": 28}}]}, "47": {"loc": {"start": {"line": 574, "column": 13}, "end": {"line": 574, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 574, "column": 13}, "end": {"line": 574, "column": 32}}, {"start": {"line": 574, "column": 36}, "end": {"line": 574, "column": 38}}]}, "48": {"loc": {"start": {"line": 575, "column": 18}, "end": {"line": 575, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 575, "column": 18}, "end": {"line": 575, "column": 32}}, {"start": {"line": 575, "column": 36}, "end": {"line": 575, "column": 55}}]}, "49": {"loc": {"start": {"line": 619, "column": 29}, "end": {"line": 619, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 619, "column": 29}, "end": {"line": 619, "column": 60}}, {"start": {"line": 619, "column": 64}, "end": {"line": 619, "column": 65}}]}, "50": {"loc": {"start": {"line": 621, "column": 23}, "end": {"line": 623, "column": 41}}, "type": "cond-expr", "locations": [{"start": {"line": 622, "column": 8}, "end": {"line": 622, "column": 76}}, {"start": {"line": 623, "column": 8}, "end": {"line": 623, "column": 41}}]}, "51": {"loc": {"start": {"line": 623, "column": 8}, "end": {"line": 623, "column": 41}}, "type": "cond-expr", "locations": [{"start": {"line": 623, "column": 35}, "end": {"line": 623, "column": 37}}, {"start": {"line": 623, "column": 40}, "end": {"line": 623, "column": 41}}]}, "52": {"loc": {"start": {"line": 629, "column": 16}, "end": {"line": 630, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 629, "column": 43}, "end": {"line": 629, "column": 52}}, {"start": {"line": 630, "column": 16}, "end": {"line": 630, "column": 62}}]}, "53": {"loc": {"start": {"line": 630, "column": 16}, "end": {"line": 630, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 630, "column": 43}, "end": {"line": 630, "column": 51}}, {"start": {"line": 630, "column": 54}, "end": {"line": 630, "column": 62}}]}, "54": {"loc": {"start": {"line": 632, "column": 21}, "end": {"line": 636, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 633, "column": 11}, "end": {"line": 635, "column": 58}}, {"start": {"line": 636, "column": 10}, "end": {"line": 636, "column": 56}}]}, "55": {"loc": {"start": {"line": 633, "column": 11}, "end": {"line": 635, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 633, "column": 27}, "end": {"line": 633, "column": 44}}, {"start": {"line": 634, "column": 11}, "end": {"line": 635, "column": 58}}]}, "56": {"loc": {"start": {"line": 634, "column": 11}, "end": {"line": 635, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 634, "column": 27}, "end": {"line": 634, "column": 51}}, {"start": {"line": 635, "column": 11}, "end": {"line": 635, "column": 58}}]}, "57": {"loc": {"start": {"line": 635, "column": 11}, "end": {"line": 635, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 635, "column": 27}, "end": {"line": 635, "column": 37}}, {"start": {"line": 635, "column": 40}, "end": {"line": 635, "column": 58}}]}, "58": {"loc": {"start": {"line": 636, "column": 10}, "end": {"line": 636, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 636, "column": 37}, "end": {"line": 636, "column": 45}}, {"start": {"line": 636, "column": 48}, "end": {"line": 636, "column": 56}}]}, "59": {"loc": {"start": {"line": 639, "column": 29}, "end": {"line": 641, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 640, "column": 10}, "end": {"line": 640, "column": 89}}, {"start": {"line": 641, "column": 10}, "end": {"line": 641, "column": 64}}]}, "60": {"loc": {"start": {"line": 641, "column": 10}, "end": {"line": 641, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 641, "column": 37}, "end": {"line": 641, "column": 45}}, {"start": {"line": 641, "column": 48}, "end": {"line": 641, "column": 64}}]}, "61": {"loc": {"start": {"line": 671, "column": 10}, "end": {"line": 671, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 671, "column": 10}, "end": {"line": 671, "column": 40}}, {"start": {"line": 671, "column": 44}, "end": {"line": 671, "column": 76}}]}, "62": {"loc": {"start": {"line": 672, "column": 12}, "end": {"line": 672, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 672, "column": 12}, "end": {"line": 672, "column": 20}}, {"start": {"line": 672, "column": 24}, "end": {"line": 672, "column": 48}}]}, "63": {"loc": {"start": {"line": 704, "column": 16}, "end": {"line": 704, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 704, "column": 16}, "end": {"line": 704, "column": 28}}, {"start": {"line": 704, "column": 32}, "end": {"line": 704, "column": 50}}]}, "64": {"loc": {"start": {"line": 736, "column": 27}, "end": {"line": 736, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 736, "column": 27}, "end": {"line": 736, "column": 56}}, {"start": {"line": 736, "column": 60}, "end": {"line": 736, "column": 64}}]}, "65": {"loc": {"start": {"line": 737, "column": 20}, "end": {"line": 737, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 737, "column": 20}, "end": {"line": 737, "column": 55}}, {"start": {"line": 737, "column": 59}, "end": {"line": 737, "column": 62}}]}, "66": {"loc": {"start": {"line": 738, "column": 27}, "end": {"line": 738, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 738, "column": 27}, "end": {"line": 738, "column": 56}}, {"start": {"line": 738, "column": 60}, "end": {"line": 738, "column": 62}}]}, "67": {"loc": {"start": {"line": 739, "column": 26}, "end": {"line": 739, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 739, "column": 26}, "end": {"line": 739, "column": 54}}, {"start": {"line": 739, "column": 58}, "end": {"line": 739, "column": 60}}]}, "68": {"loc": {"start": {"line": 765, "column": 25}, "end": {"line": 765, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 765, "column": 25}, "end": {"line": 765, "column": 52}}, {"start": {"line": 765, "column": 56}, "end": {"line": 765, "column": 58}}]}, "69": {"loc": {"start": {"line": 766, "column": 29}, "end": {"line": 766, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 766, "column": 29}, "end": {"line": 766, "column": 60}}, {"start": {"line": 766, "column": 64}, "end": {"line": 766, "column": 65}}]}, "70": {"loc": {"start": {"line": 767, "column": 28}, "end": {"line": 767, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 767, "column": 28}, "end": {"line": 767, "column": 59}}, {"start": {"line": 767, "column": 63}, "end": {"line": 767, "column": 64}}]}, "71": {"loc": {"start": {"line": 768, "column": 30}, "end": {"line": 768, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 768, "column": 30}, "end": {"line": 768, "column": 63}}, {"start": {"line": 768, "column": 67}, "end": {"line": 768, "column": 68}}]}, "72": {"loc": {"start": {"line": 774, "column": 24}, "end": {"line": 774, "column": 97}}, "type": "cond-expr", "locations": [{"start": {"line": 774, "column": 48}, "end": {"line": 774, "column": 54}}, {"start": {"line": 774, "column": 57}, "end": {"line": 774, "column": 97}}]}, "73": {"loc": {"start": {"line": 774, "column": 57}, "end": {"line": 774, "column": 97}}, "type": "cond-expr", "locations": [{"start": {"line": 774, "column": 81}, "end": {"line": 774, "column": 89}}, {"start": {"line": 774, "column": 92}, "end": {"line": 774, "column": 97}}]}, "74": {"loc": {"start": {"line": 803, "column": 4}, "end": {"line": 810, "column": 5}}, "type": "if", "locations": [{"start": {"line": 803, "column": 4}, "end": {"line": 810, "column": 5}}]}, "75": {"loc": {"start": {"line": 844, "column": 28}, "end": {"line": 844, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 844, "column": 28}, "end": {"line": 844, "column": 58}}, {"start": {"line": 844, "column": 62}, "end": {"line": 844, "column": 66}}]}, "76": {"loc": {"start": {"line": 845, "column": 24}, "end": {"line": 845, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 845, "column": 24}, "end": {"line": 845, "column": 52}}, {"start": {"line": 845, "column": 56}, "end": {"line": 845, "column": 60}}]}, "77": {"loc": {"start": {"line": 846, "column": 23}, "end": {"line": 846, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 846, "column": 23}, "end": {"line": 846, "column": 60}}, {"start": {"line": 846, "column": 64}, "end": {"line": 846, "column": 68}}]}, "78": {"loc": {"start": {"line": 847, "column": 28}, "end": {"line": 847, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 847, "column": 28}, "end": {"line": 847, "column": 58}}, {"start": {"line": 847, "column": 62}, "end": {"line": 847, "column": 64}}]}, "79": {"loc": {"start": {"line": 878, "column": 27}, "end": {"line": 878, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 878, "column": 27}, "end": {"line": 878, "column": 56}}, {"start": {"line": 878, "column": 60}, "end": {"line": 878, "column": 63}}]}, "80": {"loc": {"start": {"line": 879, "column": 31}, "end": {"line": 879, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 879, "column": 31}, "end": {"line": 879, "column": 64}}, {"start": {"line": 879, "column": 68}, "end": {"line": 879, "column": 71}}]}, "81": {"loc": {"start": {"line": 880, "column": 28}, "end": {"line": 880, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 880, "column": 28}, "end": {"line": 880, "column": 58}}, {"start": {"line": 880, "column": 62}, "end": {"line": 880, "column": 63}}]}, "82": {"loc": {"start": {"line": 881, "column": 23}, "end": {"line": 881, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 881, "column": 23}, "end": {"line": 881, "column": 60}}, {"start": {"line": 881, "column": 64}, "end": {"line": 881, "column": 68}}]}, "83": {"loc": {"start": {"line": 882, "column": 28}, "end": {"line": 882, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 882, "column": 28}, "end": {"line": 882, "column": 58}}, {"start": {"line": 882, "column": 62}, "end": {"line": 882, "column": 63}}]}, "84": {"loc": {"start": {"line": 884, "column": 24}, "end": {"line": 884, "column": 111}}, "type": "cond-expr", "locations": [{"start": {"line": 884, "column": 45}, "end": {"line": 884, "column": 101}}, {"start": {"line": 884, "column": 104}, "end": {"line": 884, "column": 111}}]}, "85": {"loc": {"start": {"line": 885, "column": 23}, "end": {"line": 885, "column": 121}}, "type": "cond-expr", "locations": [{"start": {"line": 885, "column": 47}, "end": {"line": 885, "column": 53}}, {"start": {"line": 885, "column": 56}, "end": {"line": 885, "column": 121}}]}, "86": {"loc": {"start": {"line": 935, "column": 20}, "end": {"line": 935, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 935, "column": 20}, "end": {"line": 935, "column": 47}}, {"start": {"line": 935, "column": 51}, "end": {"line": 935, "column": 52}}]}, "87": {"loc": {"start": {"line": 936, "column": 22}, "end": {"line": 936, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 936, "column": 22}, "end": {"line": 936, "column": 51}}, {"start": {"line": 936, "column": 55}, "end": {"line": 936, "column": 56}}]}, "88": {"loc": {"start": {"line": 937, "column": 24}, "end": {"line": 937, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 937, "column": 24}, "end": {"line": 937, "column": 55}}, {"start": {"line": 937, "column": 59}, "end": {"line": 937, "column": 60}}]}, "89": {"loc": {"start": {"line": 938, "column": 21}, "end": {"line": 938, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 938, "column": 21}, "end": {"line": 938, "column": 49}}, {"start": {"line": 938, "column": 53}, "end": {"line": 938, "column": 54}}]}}, "s": {"0": 4, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 4, "143": 4, "144": 4, "145": 4, "146": 4, "147": 4, "148": 4, "149": 4, "150": 4, "151": 4, "152": 4, "153": 4, "154": 4, "155": 4, "156": 4, "157": 4, "158": 4, "159": 4, "160": 4, "161": 4, "162": 4, "163": 4, "164": 4, "165": 4}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0], "34": [0, 0], "35": [0], "36": [0], "37": [0, 0], "38": [0, 0], "39": [0, 0], "40": [0, 0], "41": [0, 0], "42": [0, 0], "43": [0, 0], "44": [0, 0], "45": [0, 0], "46": [0, 0], "47": [0, 0], "48": [0, 0], "49": [0, 0], "50": [0, 0], "51": [0, 0], "52": [0, 0], "53": [0, 0], "54": [0, 0], "55": [0, 0], "56": [0, 0], "57": [0, 0], "58": [0, 0], "59": [0, 0], "60": [0, 0], "61": [0, 0], "62": [0, 0], "63": [0, 0], "64": [0, 0], "65": [0, 0], "66": [0, 0], "67": [0, 0], "68": [0, 0], "69": [0, 0], "70": [0, 0], "71": [0, 0], "72": [0, 0], "73": [0, 0], "74": [0], "75": [0, 0], "76": [0, 0], "77": [0, 0], "78": [0, 0], "79": [0, 0], "80": [0, 0], "81": [0, 0], "82": [0, 0], "83": [0, 0], "84": [0, 0], "85": [0, 0], "86": [0, 0], "87": [0, 0], "88": [0, 0], "89": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/database/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/database/index.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 38}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 4}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/use-toast.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/use-toast.ts", "statementMap": {"0": {"start": {"line": 191, "column": 9}, "end": {"line": 191, "column": 17}}, "1": {"start": {"line": 191, "column": 19}, "end": {"line": 191, "column": 24}}, "2": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "3": {"start": {"line": 8, "column": 20}, "end": {"line": 8, "column": 21}}, "4": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 34}}, "5": {"start": {"line": 18, "column": 20}, "end": {"line": 23, "column": null}}, "6": {"start": {"line": 25, "column": 12}, "end": {"line": 25, "column": 13}}, "7": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": null}}, "8": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": null}}, "9": {"start": {"line": 56, "column": 22}, "end": {"line": 56, "column": 70}}, "10": {"start": {"line": 58, "column": 25}, "end": {"line": 72, "column": 1}}, "11": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, "12": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 10}}, "13": {"start": {"line": 63, "column": 18}, "end": {"line": 69, "column": 24}}, "14": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": null}}, "15": {"start": {"line": 65, "column": 4}, "end": {"line": 68, "column": null}}, "16": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": null}}, "17": {"start": {"line": 74, "column": 23}, "end": {"line": 127, "column": 1}}, "18": {"start": {"line": 75, "column": 2}, "end": {"line": 126, "column": 3}}, "19": {"start": {"line": 77, "column": 6}, "end": {"line": 80, "column": null}}, "20": {"start": {"line": 83, "column": 6}, "end": {"line": 88, "column": null}}, "21": {"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": 66}}, "22": {"start": {"line": 91, "column": 26}, "end": {"line": 91, "column": 32}}, "23": {"start": {"line": 95, "column": 6}, "end": {"line": 101, "column": 7}}, "24": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": null}}, "25": {"start": {"line": 98, "column": 8}, "end": {"line": 100, "column": null}}, "26": {"start": {"line": 99, "column": 10}, "end": {"line": 99, "column": null}}, "27": {"start": {"line": 103, "column": 6}, "end": {"line": 113, "column": null}}, "28": {"start": {"line": 106, "column": 10}, "end": {"line": 111, "column": 15}}, "29": {"start": {"line": 116, "column": 6}, "end": {"line": 121, "column": 7}}, "30": {"start": {"line": 117, "column": 8}, "end": {"line": 120, "column": null}}, "31": {"start": {"line": 122, "column": 6}, "end": {"line": 125, "column": null}}, "32": {"start": {"line": 124, "column": 43}, "end": {"line": 124, "column": 66}}, "33": {"start": {"line": 74, "column": 13}, "end": {"line": 74, "column": 23}}, "34": {"start": {"line": 129, "column": 49}, "end": {"line": 129, "column": 51}}, "35": {"start": {"line": 131, "column": 25}, "end": {"line": 131, "column": 39}}, "36": {"start": {"line": 134, "column": 2}, "end": {"line": 134, "column": null}}, "37": {"start": {"line": 135, "column": 2}, "end": {"line": 137, "column": null}}, "38": {"start": {"line": 136, "column": 4}, "end": {"line": 136, "column": null}}, "39": {"start": {"line": 143, "column": 13}, "end": {"line": 143, "column": 20}}, "40": {"start": {"line": 145, "column": 17}, "end": {"line": 149, "column": 6}}, "41": {"start": {"line": 146, "column": 4}, "end": {"line": 149, "column": 6}}, "42": {"start": {"line": 150, "column": 18}, "end": {"line": 150, "column": 72}}, "43": {"start": {"line": 150, "column": 24}, "end": {"line": 150, "column": 72}}, "44": {"start": {"line": 152, "column": 2}, "end": {"line": 162, "column": null}}, "45": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": null}}, "46": {"start": {"line": 159, "column": 19}, "end": {"line": 159, "column": null}}, "47": {"start": {"line": 164, "column": 2}, "end": {"line": 168, "column": null}}, "48": {"start": {"line": 172, "column": 28}, "end": {"line": 172, "column": 62}}, "49": {"start": {"line": 174, "column": 2}, "end": {"line": 182, "column": null}}, "50": {"start": {"line": 175, "column": 4}, "end": {"line": 175, "column": null}}, "51": {"start": {"line": 176, "column": 4}, "end": {"line": 181, "column": null}}, "52": {"start": {"line": 177, "column": 20}, "end": {"line": 177, "column": 47}}, "53": {"start": {"line": 178, "column": 6}, "end": {"line": 180, "column": 7}}, "54": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": null}}, "55": {"start": {"line": 184, "column": 2}, "end": {"line": 188, "column": null}}, "56": {"start": {"line": 187, "column": 35}, "end": {"line": 187, "column": 79}}}, "fnMap": {"0": {"name": "genId", "decl": {"start": {"line": 27, "column": 9}, "end": {"line": 27, "column": 14}}, "loc": {"start": {"line": 27, "column": 14}, "end": {"line": 30, "column": 1}}}, "1": {"name": "(anonymous_10)", "decl": {"start": {"line": 58, "column": 25}, "end": {"line": 58, "column": 26}}, "loc": {"start": {"line": 58, "column": 45}, "end": {"line": 72, "column": 1}}}, "2": {"name": "(anonymous_11)", "decl": {"start": {"line": 63, "column": 29}, "end": {"line": 63, "column": 32}}, "loc": {"start": {"line": 63, "column": 34}, "end": {"line": 69, "column": 3}}}, "3": {"name": "(anonymous_12)", "decl": {"start": {"line": 74, "column": 23}, "end": {"line": 74, "column": 24}}, "loc": {"start": {"line": 74, "column": 63}, "end": {"line": 127, "column": 1}}}, "4": {"name": "(anonymous_13)", "decl": {"start": {"line": 85, "column": 33}, "end": {"line": 85, "column": 34}}, "loc": {"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": 66}}}, "5": {"name": "(anonymous_14)", "decl": {"start": {"line": 98, "column": 29}, "end": {"line": 98, "column": 30}}, "loc": {"start": {"line": 98, "column": 39}, "end": {"line": 100, "column": 9}}}, "6": {"name": "(anonymous_15)", "decl": {"start": {"line": 105, "column": 33}, "end": {"line": 105, "column": 34}}, "loc": {"start": {"line": 106, "column": 10}, "end": {"line": 111, "column": 15}}}, "7": {"name": "(anonymous_16)", "decl": {"start": {"line": 124, "column": 36}, "end": {"line": 124, "column": 37}}, "loc": {"start": {"line": 124, "column": 43}, "end": {"line": 124, "column": 66}}}, "8": {"name": "dispatch", "decl": {"start": {"line": 133, "column": 9}, "end": {"line": 133, "column": 17}}, "loc": {"start": {"line": 133, "column": 32}, "end": {"line": 138, "column": 1}}}, "9": {"name": "(anonymous_18)", "decl": {"start": {"line": 135, "column": 20}, "end": {"line": 135, "column": 21}}, "loc": {"start": {"line": 135, "column": 33}, "end": {"line": 137, "column": 3}}}, "10": {"name": "toast", "decl": {"start": {"line": 142, "column": 9}, "end": {"line": 142, "column": 14}}, "loc": {"start": {"line": 142, "column": 34}, "end": {"line": 169, "column": 1}}}, "11": {"name": "(anonymous_20)", "decl": {"start": {"line": 145, "column": 17}, "end": {"line": 145, "column": 18}}, "loc": {"start": {"line": 146, "column": 4}, "end": {"line": 149, "column": 6}}}, "12": {"name": "(anonymous_21)", "decl": {"start": {"line": 150, "column": 18}, "end": {"line": 150, "column": 21}}, "loc": {"start": {"line": 150, "column": 24}, "end": {"line": 150, "column": 72}}}, "13": {"name": "(anonymous_22)", "decl": {"start": {"line": 158, "column": 20}, "end": {"line": 158, "column": 21}}, "loc": {"start": {"line": 158, "column": 29}, "end": {"line": 160, "column": 7}}}, "14": {"name": "useToast", "decl": {"start": {"line": 171, "column": 9}, "end": {"line": 171, "column": 17}}, "loc": {"start": {"line": 171, "column": 17}, "end": {"line": 189, "column": 1}}}, "15": {"name": "(anonymous_24)", "decl": {"start": {"line": 174, "column": 18}, "end": {"line": 174, "column": 21}}, "loc": {"start": {"line": 174, "column": 23}, "end": {"line": 182, "column": 3}}}, "16": {"name": "(anonymous_25)", "decl": {"start": {"line": 176, "column": 11}, "end": {"line": 176, "column": 14}}, "loc": {"start": {"line": 176, "column": 16}, "end": {"line": 181, "column": 5}}}, "17": {"name": "(anonymous_26)", "decl": {"start": {"line": 187, "column": 13}, "end": {"line": 187, "column": 14}}, "loc": {"start": {"line": 187, "column": 35}, "end": {"line": 187, "column": 79}}}}, "branchMap": {"0": {"loc": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, "type": "if", "locations": [{"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}]}, "1": {"loc": {"start": {"line": 75, "column": 2}, "end": {"line": 126, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 76, "column": 4}, "end": {"line": 80, "column": null}}, {"start": {"line": 82, "column": 4}, "end": {"line": 88, "column": null}}, {"start": {"line": 90, "column": 4}, "end": {"line": 114, "column": 5}}, {"start": {"line": 115, "column": 4}, "end": {"line": 125, "column": null}}]}, "2": {"loc": {"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 86, "column": 37}, "end": {"line": 86, "column": 62}}, {"start": {"line": 86, "column": 65}, "end": {"line": 86, "column": 66}}]}, "3": {"loc": {"start": {"line": 95, "column": 6}, "end": {"line": 101, "column": 7}}, "type": "if", "locations": [{"start": {"line": 95, "column": 6}, "end": {"line": 101, "column": 7}}, {"start": {"line": 97, "column": 13}, "end": {"line": 101, "column": 7}}]}, "4": {"loc": {"start": {"line": 106, "column": 10}, "end": {"line": 111, "column": 15}}, "type": "cond-expr", "locations": [{"start": {"line": 107, "column": 14}, "end": {"line": 110, "column": null}}, {"start": {"line": 111, "column": 14}, "end": {"line": 111, "column": 15}}]}, "5": {"loc": {"start": {"line": 106, "column": 10}, "end": {"line": 106, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 106, "column": 10}, "end": {"line": 106, "column": 26}}, {"start": {"line": 106, "column": 30}, "end": {"line": 106, "column": 51}}]}, "6": {"loc": {"start": {"line": 116, "column": 6}, "end": {"line": 121, "column": 7}}, "type": "if", "locations": [{"start": {"line": 116, "column": 6}, "end": {"line": 121, "column": 7}}]}, "7": {"loc": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": null}}, "type": "if", "locations": [{"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": null}}]}, "8": {"loc": {"start": {"line": 178, "column": 6}, "end": {"line": 180, "column": 7}}, "type": "if", "locations": [{"start": {"line": 178, "column": 6}, "end": {"line": 180, "column": 7}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "b": {"0": [0], "1": [0, 0, 0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0], "7": [0], "8": [0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/useComplianceApi.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/useComplianceApi.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 78}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 77}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 31}}, "3": {"start": {"line": 7, "column": 33}, "end": {"line": 20, "column": 1}}, "4": {"start": {"line": 8, "column": 22}, "end": {"line": 8, "column": 38}}, "5": {"start": {"line": 10, "column": 2}, "end": {"line": 19, "column": 5}}, "6": {"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": 63}}, "7": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 65}}, "8": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 65}}, "9": {"start": {"line": 7, "column": 13}, "end": {"line": 7, "column": 33}}, "10": {"start": {"line": 23, "column": 37}, "end": {"line": 37, "column": 1}}, "11": {"start": {"line": 24, "column": 22}, "end": {"line": 24, "column": 38}}, "12": {"start": {"line": 26, "column": 2}, "end": {"line": 36, "column": 5}}, "13": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 58}}, "14": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 76}}, "15": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 65}}, "16": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 65}}, "17": {"start": {"line": 23, "column": 13}, "end": {"line": 23, "column": 37}}, "18": {"start": {"line": 40, "column": 28}, "end": {"line": 53, "column": 1}}, "19": {"start": {"line": 41, "column": 22}, "end": {"line": 41, "column": 38}}, "20": {"start": {"line": 43, "column": 2}, "end": {"line": 52, "column": 5}}, "21": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 48}}, "22": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 59}}, "23": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 60}}, "24": {"start": {"line": 40, "column": 13}, "end": {"line": 40, "column": 28}}, "25": {"start": {"line": 56, "column": 37}, "end": {"line": 70, "column": 1}}, "26": {"start": {"line": 57, "column": 22}, "end": {"line": 57, "column": 38}}, "27": {"start": {"line": 59, "column": 2}, "end": {"line": 69, "column": 5}}, "28": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 61}}, "29": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 59}}, "30": {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": 59}}, "31": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 70}}, "32": {"start": {"line": 56, "column": 13}, "end": {"line": 56, "column": 37}}, "33": {"start": {"line": 73, "column": 33}, "end": {"line": 86, "column": 1}}, "34": {"start": {"line": 74, "column": 22}, "end": {"line": 74, "column": 38}}, "35": {"start": {"line": 76, "column": 2}, "end": {"line": 85, "column": 5}}, "36": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 49}}, "37": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 63}}, "38": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 65}}, "39": {"start": {"line": 73, "column": 13}, "end": {"line": 73, "column": 33}}, "40": {"start": {"line": 89, "column": 42}, "end": {"line": 102, "column": 1}}, "41": {"start": {"line": 90, "column": 22}, "end": {"line": 90, "column": 38}}, "42": {"start": {"line": 92, "column": 2}, "end": {"line": 101, "column": 5}}, "43": {"start": {"line": 95, "column": 6}, "end": {"line": 95, "column": 58}}, "44": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 66}}, "45": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 71}}, "46": {"start": {"line": 89, "column": 13}, "end": {"line": 89, "column": 42}}, "47": {"start": {"line": 105, "column": 33}, "end": {"line": 148, "column": 1}}, "48": {"start": {"line": 106, "column": 2}, "end": {"line": 147, "column": 5}}, "49": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 35}}, "50": {"start": {"line": 109, "column": 23}, "end": {"line": 109, "column": 35}}, "51": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 68}}, "52": {"start": {"line": 112, "column": 21}, "end": {"line": 112, "column": 70}}, "53": {"start": {"line": 113, "column": 6}, "end": {"line": 113, "column": 73}}, "54": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 20}}, "55": {"start": {"line": 120, "column": 6}, "end": {"line": 123, "column": 7}}, "56": {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 69}}, "57": {"start": {"line": 122, "column": 8}, "end": {"line": 122, "column": 21}}, "58": {"start": {"line": 126, "column": 27}, "end": {"line": 126, "column": 43}}, "59": {"start": {"line": 127, "column": 28}, "end": {"line": 127, "column": 48}}, "60": {"start": {"line": 130, "column": 6}, "end": {"line": 133, "column": 7}}, "61": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 77}}, "62": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 21}}, "63": {"start": {"line": 135, "column": 6}, "end": {"line": 138, "column": 7}}, "64": {"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": 60}}, "65": {"start": {"line": 137, "column": 8}, "end": {"line": 137, "column": 21}}, "66": {"start": {"line": 141, "column": 6}, "end": {"line": 141, "column": 82}}, "67": {"start": {"line": 142, "column": 6}, "end": {"line": 142, "column": 18}}, "68": {"start": {"line": 105, "column": 13}, "end": {"line": 105, "column": 33}}, "69": {"start": {"line": 151, "column": 33}, "end": {"line": 157, "column": 1}}, "70": {"start": {"line": 152, "column": 2}, "end": {"line": 156, "column": 5}}, "71": {"start": {"line": 154, "column": 19}, "end": {"line": 154, "column": 82}}, "72": {"start": {"line": 151, "column": 13}, "end": {"line": 151, "column": 33}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 33}, "end": {"line": 7, "column": 36}}, "loc": {"start": {"line": 7, "column": 38}, "end": {"line": 20, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 12, "column": 15}, "end": {"line": 12, "column": 18}}, "loc": {"start": {"line": 12, "column": 20}, "end": {"line": 15, "column": 5}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 13}, "end": {"line": 16, "column": 14}}, "loc": {"start": {"line": 16, "column": 23}, "end": {"line": 18, "column": 5}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 23, "column": 37}, "end": {"line": 23, "column": 40}}, "loc": {"start": {"line": 23, "column": 42}, "end": {"line": 37, "column": 1}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 27, "column": 16}, "end": {"line": 27, "column": 17}}, "loc": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 58}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 29, "column": 15}, "end": {"line": 29, "column": 18}}, "loc": {"start": {"line": 29, "column": 20}, "end": {"line": 32, "column": 5}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 33, "column": 13}, "end": {"line": 33, "column": 14}}, "loc": {"start": {"line": 33, "column": 23}, "end": {"line": 35, "column": 5}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 40, "column": 28}, "end": {"line": 40, "column": 31}}, "loc": {"start": {"line": 40, "column": 33}, "end": {"line": 53, "column": 1}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 45, "column": 15}, "end": {"line": 45, "column": 18}}, "loc": {"start": {"line": 45, "column": 20}, "end": {"line": 48, "column": 5}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 49, "column": 13}, "end": {"line": 49, "column": 14}}, "loc": {"start": {"line": 49, "column": 23}, "end": {"line": 51, "column": 5}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 56, "column": 37}, "end": {"line": 56, "column": 40}}, "loc": {"start": {"line": 56, "column": 42}, "end": {"line": 70, "column": 1}}}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 60, "column": 16}, "end": {"line": 60, "column": 17}}, "loc": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 61}}}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 62, "column": 15}, "end": {"line": 62, "column": 18}}, "loc": {"start": {"line": 62, "column": 20}, "end": {"line": 65, "column": 5}}}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 66, "column": 13}, "end": {"line": 66, "column": 14}}, "loc": {"start": {"line": 66, "column": 23}, "end": {"line": 68, "column": 5}}}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 73, "column": 33}, "end": {"line": 73, "column": 36}}, "loc": {"start": {"line": 73, "column": 38}, "end": {"line": 86, "column": 1}}}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 78, "column": 15}, "end": {"line": 78, "column": 18}}, "loc": {"start": {"line": 78, "column": 20}, "end": {"line": 81, "column": 5}}}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 82, "column": 13}, "end": {"line": 82, "column": 14}}, "loc": {"start": {"line": 82, "column": 23}, "end": {"line": 84, "column": 5}}}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 89, "column": 42}, "end": {"line": 89, "column": 45}}, "loc": {"start": {"line": 89, "column": 47}, "end": {"line": 102, "column": 1}}}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 94, "column": 15}, "end": {"line": 94, "column": 18}}, "loc": {"start": {"line": 94, "column": 20}, "end": {"line": 97, "column": 5}}}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 98, "column": 13}, "end": {"line": 98, "column": 14}}, "loc": {"start": {"line": 98, "column": 23}, "end": {"line": 100, "column": 5}}}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 105, "column": 33}, "end": {"line": 105, "column": 34}}, "loc": {"start": {"line": 105, "column": 88}, "end": {"line": 148, "column": 1}}}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 108, "column": 13}, "end": {"line": 108, "column": 18}}, "loc": {"start": {"line": 108, "column": 24}, "end": {"line": 116, "column": 5}}}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 118, "column": 21}, "end": {"line": 118, "column": 22}}, "loc": {"start": {"line": 118, "column": 31}, "end": {"line": 143, "column": 5}}}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 151, "column": 33}, "end": {"line": 151, "column": 34}}, "loc": {"start": {"line": 151, "column": 88}, "end": {"line": 157, "column": 1}}}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 154, "column": 13}, "end": {"line": 154, "column": 16}}, "loc": {"start": {"line": 154, "column": 19}, "end": {"line": 154, "column": 82}}}}, "branchMap": {"0": {"loc": {"start": {"line": 105, "column": 61}, "end": {"line": 105, "column": 84}}, "type": "default-arg", "locations": [{"start": {"line": 105, "column": 80}, "end": {"line": 105, "column": 84}}]}, "1": {"loc": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 35}}, "type": "if", "locations": [{"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 35}}]}, "2": {"loc": {"start": {"line": 117, "column": 13}, "end": {"line": 117, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 117, "column": 13}, "end": {"line": 117, "column": 20}}, {"start": {"line": 117, "column": 24}, "end": {"line": 117, "column": 36}}]}, "3": {"loc": {"start": {"line": 120, "column": 6}, "end": {"line": 123, "column": 7}}, "type": "if", "locations": [{"start": {"line": 120, "column": 6}, "end": {"line": 123, "column": 7}}]}, "4": {"loc": {"start": {"line": 130, "column": 6}, "end": {"line": 133, "column": 7}}, "type": "if", "locations": [{"start": {"line": 130, "column": 6}, "end": {"line": 133, "column": 7}}]}, "5": {"loc": {"start": {"line": 135, "column": 6}, "end": {"line": 138, "column": 7}}, "type": "if", "locations": [{"start": {"line": 135, "column": 6}, "end": {"line": 138, "column": 7}}]}, "6": {"loc": {"start": {"line": 151, "column": 61}, "end": {"line": 151, "column": 84}}, "type": "default-arg", "locations": [{"start": {"line": 151, "column": 80}, "end": {"line": 151, "column": 84}}]}, "7": {"loc": {"start": {"line": 154, "column": 19}, "end": {"line": 154, "column": 82}}, "type": "cond-expr", "locations": [{"start": {"line": 154, "column": 32}, "end": {"line": 154, "column": 75}}, {"start": {"line": 154, "column": 78}, "end": {"line": 154, "column": 82}}]}, "8": {"loc": {"start": {"line": 155, "column": 13}, "end": {"line": 155, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 155, "column": 13}, "end": {"line": 155, "column": 20}}, {"start": {"line": 155, "column": 24}, "end": {"line": 155, "column": 36}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0, 0], "8": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/lib/utils.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/lib/utils.ts", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 16}}, "1": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "2": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "3": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": null}}}, "fnMap": {"0": {"name": "cn", "decl": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 18}}, "loc": {"start": {"line": 4, "column": 42}, "end": {"line": 6, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/cors.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/cors.ts", "statementMap": {"0": {"start": {"line": 4, "column": 30}, "end": {"line": 14, "column": 1}}, "1": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 49}}, "2": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 80}}, "3": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 110}}, "4": {"start": {"line": 9, "column": 2}, "end": {"line": 13, "column": 3}}, "5": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 24}}, "6": {"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": 11}}, "7": {"start": {"line": 4, "column": 13}, "end": {"line": 4, "column": 30}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": 31}}, "loc": {"start": {"line": 4, "column": 82}, "end": {"line": 14, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 2}, "end": {"line": 13, "column": 3}}, "type": "if", "locations": [{"start": {"line": 9, "column": 2}, "end": {"line": 13, "column": 3}}, {"start": {"line": 11, "column": 9}, "end": {"line": 13, "column": 3}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/error-handler.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/error-handler.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "1": {"start": {"line": 4, "column": 28}, "end": {"line": 15, "column": 1}}, "2": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 76}}, "3": {"start": {"line": 7, "column": 2}, "end": {"line": 9, "column": 3}}, "4": {"start": {"line": 8, "column": 4}, "end": {"line": 8, "column": 23}}, "5": {"start": {"line": 11, "column": 2}, "end": {"line": 14, "column": 5}}, "6": {"start": {"line": 4, "column": 13}, "end": {"line": 4, "column": 28}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 29}}, "loc": {"start": {"line": 4, "column": 94}, "end": {"line": 15, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 7, "column": 2}, "end": {"line": 9, "column": 3}}, "type": "if", "locations": [{"start": {"line": 7, "column": 2}, "end": {"line": 9, "column": 3}}]}, "1": {"loc": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 92}}, "type": "cond-expr", "locations": [{"start": {"line": 13, "column": 54}, "end": {"line": 13, "column": 67}}, {"start": {"line": 13, "column": 70}, "end": {"line": 13, "column": 92}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0}, "b": {"0": [0], "1": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/index.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 23}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 25}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 32}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/upload.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/middleware/upload.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 28}}, "1": {"start": {"line": 4, "column": 13}, "end": {"line": 25, "column": 3}}, "2": {"start": {"line": 10, "column": 25}, "end": {"line": 17, "column": 6}}, "3": {"start": {"line": 19, "column": 4}, "end": {"line": 23, "column": 5}}, "4": {"start": {"line": 20, "column": 6}, "end": {"line": 20, "column": 21}}, "5": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 41}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 9, "column": 14}, "end": {"line": 9, "column": 15}}, "loc": {"start": {"line": 9, "column": 32}, "end": {"line": 24, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 4}, "end": {"line": 23, "column": 5}}, "type": "if", "locations": [{"start": {"line": 19, "column": 4}, "end": {"line": 23, "column": 5}}, {"start": {"line": 21, "column": 11}, "end": {"line": 23, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/compliance.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/compliance.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 20}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 48}}, "4": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 49}}, "5": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 51}}, "6": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 46}}, "7": {"start": {"line": 10, "column": 15}, "end": {"line": 10, "column": 23}}, "8": {"start": {"line": 13, "column": 0}, "end": {"line": 33, "column": 3}}, "9": {"start": {"line": 14, "column": 2}, "end": {"line": 32, "column": 3}}, "10": {"start": {"line": 15, "column": 41}, "end": {"line": 15, "column": 49}}, "11": {"start": {"line": 18, "column": 19}, "end": {"line": 21, "column": 41}}, "12": {"start": {"line": 24, "column": 4}, "end": {"line": 28, "column": 7}}, "13": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 80}}, "14": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 61}}, "15": {"start": {"line": 36, "column": 0}, "end": {"line": 75, "column": 3}}, "16": {"start": {"line": 37, "column": 2}, "end": {"line": 74, "column": 3}}, "17": {"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 5}}, "18": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 58}}, "19": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 13}}, "20": {"start": {"line": 43, "column": 41}, "end": {"line": 43, "column": 49}}, "21": {"start": {"line": 46, "column": 24}, "end": {"line": 46, "column": 62}}, "22": {"start": {"line": 49, "column": 41}, "end": {"line": 55, "column": 6}}, "23": {"start": {"line": 58, "column": 19}, "end": {"line": 61, "column": 41}}, "24": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 33}}, "25": {"start": {"line": 66, "column": 4}, "end": {"line": 70, "column": 7}}, "26": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 79}}, "27": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 61}}, "28": {"start": {"line": 78, "column": 0}, "end": {"line": 90, "column": 3}}, "29": {"start": {"line": 79, "column": 2}, "end": {"line": 89, "column": 3}}, "30": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 50}}, "31": {"start": {"line": 83, "column": 23}, "end": {"line": 83, "column": 69}}, "32": {"start": {"line": 85, "column": 4}, "end": {"line": 85, "column": 25}}, "33": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 62}}, "34": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 73}}, "35": {"start": {"line": 93, "column": 0}, "end": {"line": 105, "column": 3}}, "36": {"start": {"line": 94, "column": 2}, "end": {"line": 104, "column": 3}}, "37": {"start": {"line": 95, "column": 4}, "end": {"line": 95, "column": 53}}, "38": {"start": {"line": 98, "column": 22}, "end": {"line": 98, "column": 71}}, "39": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 24}}, "40": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 65}}, "41": {"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": 76}}, "42": {"start": {"line": 108, "column": 0}, "end": {"line": 112, "column": 3}}, "43": {"start": {"line": 109, "column": 2}, "end": {"line": 109, "column": 48}}, "44": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 98}}, "45": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 25}, "end": {"line": 13, "column": 30}}, "loc": {"start": {"line": 13, "column": 78}, "end": {"line": 33, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 36, "column": 55}, "end": {"line": 36, "column": 60}}, "loc": {"start": {"line": 36, "column": 104}, "end": {"line": 75, "column": 1}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 78, "column": 33}, "end": {"line": 78, "column": 38}}, "loc": {"start": {"line": 78, "column": 87}, "end": {"line": 90, "column": 1}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 93, "column": 25}, "end": {"line": 93, "column": 30}}, "loc": {"start": {"line": 93, "column": 79}, "end": {"line": 105, "column": 1}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 108, "column": 34}, "end": {"line": 108, "column": 35}}, "loc": {"start": {"line": 108, "column": 73}, "end": {"line": 112, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 5}}, "type": "if", "locations": [{"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 5}}]}, "1": {"loc": {"start": {"line": 50, "column": 10}, "end": {"line": 50, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 50, "column": 10}, "end": {"line": 50, "column": 20}}, {"start": {"line": 50, "column": 24}, "end": {"line": 50, "column": 43}}]}, "2": {"loc": {"start": {"line": 52, "column": 20}, "end": {"line": 52, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 52, "column": 20}, "end": {"line": 52, "column": 32}}, {"start": {"line": 52, "column": 36}, "end": {"line": 52, "column": 47}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/dashboard.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/dashboard.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 49}}, "2": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 23}}, "3": {"start": {"line": 7, "column": 0}, "end": {"line": 19, "column": 3}}, "4": {"start": {"line": 8, "column": 2}, "end": {"line": 18, "column": 3}}, "5": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 50}}, "6": {"start": {"line": 12, "column": 20}, "end": {"line": 12, "column": 66}}, "7": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 22}}, "8": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 62}}, "9": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 73}}, "10": {"start": {"line": 22, "column": 0}, "end": {"line": 34, "column": 3}}, "11": {"start": {"line": 23, "column": 2}, "end": {"line": 33, "column": 3}}, "12": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 53}}, "13": {"start": {"line": 27, "column": 22}, "end": {"line": 27, "column": 71}}, "14": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 24}}, "15": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 65}}, "16": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 76}}, "17": {"start": {"line": 37, "column": 0}, "end": {"line": 49, "column": 3}}, "18": {"start": {"line": 38, "column": 2}, "end": {"line": 48, "column": 3}}, "19": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 50}}, "20": {"start": {"line": 42, "column": 23}, "end": {"line": 42, "column": 69}}, "21": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 25}}, "22": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 62}}, "23": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 73}}, "24": {"start": {"line": 52, "column": 0}, "end": {"line": 64, "column": 3}}, "25": {"start": {"line": 53, "column": 2}, "end": {"line": 63, "column": 3}}, "26": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 44}}, "27": {"start": {"line": 57, "column": 21}, "end": {"line": 57, "column": 61}}, "28": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 23}}, "29": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 56}}, "30": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 67}}, "31": {"start": {"line": 67, "column": 0}, "end": {"line": 79, "column": 3}}, "32": {"start": {"line": 68, "column": 2}, "end": {"line": 78, "column": 3}}, "33": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 52}}, "34": {"start": {"line": 72, "column": 24}, "end": {"line": 72, "column": 73}}, "35": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 26}}, "36": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 65}}, "37": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 76}}, "38": {"start": {"line": 82, "column": 0}, "end": {"line": 86, "column": 3}}, "39": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 48}}, "40": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 98}}, "41": {"start": {"line": 89, "column": 0}, "end": {"line": 93, "column": 3}}, "42": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": 48}}, "43": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": 99}}, "44": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 28}}, "loc": {"start": {"line": 7, "column": 77}, "end": {"line": 19, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 36}, "end": {"line": 22, "column": 41}}, "loc": {"start": {"line": 22, "column": 90}, "end": {"line": 34, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 37, "column": 33}, "end": {"line": 37, "column": 38}}, "loc": {"start": {"line": 37, "column": 87}, "end": {"line": 49, "column": 1}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 52, "column": 27}, "end": {"line": 52, "column": 32}}, "loc": {"start": {"line": 52, "column": 81}, "end": {"line": 64, "column": 1}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 67, "column": 36}, "end": {"line": 67, "column": 41}}, "loc": {"start": {"line": 67, "column": 90}, "end": {"line": 79, "column": 1}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 82, "column": 33}, "end": {"line": 82, "column": 34}}, "loc": {"start": {"line": 82, "column": 72}, "end": {"line": 86, "column": 1}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 89, "column": 34}, "end": {"line": 89, "column": 35}}, "loc": {"start": {"line": 89, "column": 73}, "end": {"line": 93, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/documents.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/documents.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 49}}, "2": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 23}}, "3": {"start": {"line": 7, "column": 0}, "end": {"line": 19, "column": 3}}, "4": {"start": {"line": 8, "column": 2}, "end": {"line": 18, "column": 3}}, "5": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 49}}, "6": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 73}}, "7": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 30}}, "8": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 61}}, "9": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 72}}, "10": {"start": {"line": 22, "column": 0}, "end": {"line": 34, "column": 3}}, "11": {"start": {"line": 23, "column": 2}, "end": {"line": 33, "column": 3}}, "12": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 47}}, "13": {"start": {"line": 27, "column": 18}, "end": {"line": 27, "column": 61}}, "14": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 20}}, "15": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 59}}, "16": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 70}}, "17": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 22}, "end": {"line": 7, "column": 27}}, "loc": {"start": {"line": 7, "column": 76}, "end": {"line": 19, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 21}, "end": {"line": 22, "column": 26}}, "loc": {"start": {"line": 22, "column": 75}, "end": {"line": 34, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/index.ts", "statementMap": {"0": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 16}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 44}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 30}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 38}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 42}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 42}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 44}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 42}}, "8": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 47}}, "9": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 33}}, "10": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 41}}, "11": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 45}}, "12": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 45}}, "13": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 47}}, "14": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 45}}}, "fnMap": {"0": {"name": "setupRoutes", "decl": {"start": {"line": 10, "column": 16}, "end": {"line": 10, "column": 27}}, "loc": {"start": {"line": 10, "column": 40}, "end": {"line": 19, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/kyc.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/kyc.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 20}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 48}}, "4": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 49}}, "5": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 44}}, "6": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 46}}, "7": {"start": {"line": 10, "column": 15}, "end": {"line": 10, "column": 23}}, "8": {"start": {"line": 13, "column": 0}, "end": {"line": 36, "column": 3}}, "9": {"start": {"line": 14, "column": 2}, "end": {"line": 35, "column": 3}}, "10": {"start": {"line": 15, "column": 32}, "end": {"line": 15, "column": 40}}, "11": {"start": {"line": 18, "column": 19}, "end": {"line": 21, "column": 33}}, "12": {"start": {"line": 24, "column": 18}, "end": {"line": 24, "column": 83}}, "13": {"start": {"line": 26, "column": 4}, "end": {"line": 31, "column": 7}}, "14": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 75}}, "15": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 61}}, "16": {"start": {"line": 39, "column": 0}, "end": {"line": 67, "column": 3}}, "17": {"start": {"line": 40, "column": 2}, "end": {"line": 66, "column": 3}}, "18": {"start": {"line": 41, "column": 18}, "end": {"line": 41, "column": 36}}, "19": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "20": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 63}}, "21": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 13}}, "22": {"start": {"line": 47, "column": 27}, "end": {"line": 47, "column": 35}}, "23": {"start": {"line": 50, "column": 4}, "end": {"line": 56, "column": 5}}, "24": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 100}}, "25": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 31}}, "26": {"start": {"line": 58, "column": 4}, "end": {"line": 62, "column": 7}}, "27": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 84}}, "28": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": 61}}, "29": {"start": {"line": 70, "column": 0}, "end": {"line": 82, "column": 3}}, "30": {"start": {"line": 71, "column": 2}, "end": {"line": 81, "column": 3}}, "31": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 42}}, "32": {"start": {"line": 75, "column": 21}, "end": {"line": 75, "column": 59}}, "33": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 23}}, "34": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 54}}, "35": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 65}}, "36": {"start": {"line": 85, "column": 0}, "end": {"line": 97, "column": 3}}, "37": {"start": {"line": 86, "column": 2}, "end": {"line": 96, "column": 3}}, "38": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 42}}, "39": {"start": {"line": 90, "column": 21}, "end": {"line": 90, "column": 59}}, "40": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 23}}, "41": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 54}}, "42": {"start": {"line": 95, "column": 4}, "end": {"line": 95, "column": 65}}, "43": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 25}, "end": {"line": 13, "column": 30}}, "loc": {"start": {"line": 13, "column": 78}, "end": {"line": 36, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 39, "column": 53}, "end": {"line": 39, "column": 58}}, "loc": {"start": {"line": 39, "column": 102}, "end": {"line": 67, "column": 1}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 70, "column": 21}, "end": {"line": 70, "column": 26}}, "loc": {"start": {"line": 70, "column": 75}, "end": {"line": 82, "column": 1}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 85, "column": 21}, "end": {"line": 85, "column": 26}}, "loc": {"start": {"line": 85, "column": 75}, "end": {"line": 97, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}]}, "1": {"loc": {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 14}}, {"start": {"line": 42, "column": 18}, "end": {"line": 42, "column": 36}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/regulatory.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/regulatory.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 66}}, "4": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 23}}, "5": {"start": {"line": 9, "column": 0}, "end": {"line": 39, "column": 3}}, "6": {"start": {"line": 10, "column": 2}, "end": {"line": 38, "column": 3}}, "7": {"start": {"line": 11, "column": 58}, "end": {"line": 11, "column": 66}}, "8": {"start": {"line": 13, "column": 4}, "end": {"line": 16, "column": 5}}, "9": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 86}}, "10": {"start": {"line": 15, "column": 6}, "end": {"line": 15, "column": 13}}, "11": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 100}}, "12": {"start": {"line": 21, "column": 27}, "end": {"line": 26, "column": 8}}, "13": {"start": {"line": 28, "column": 23}, "end": {"line": 28, "column": 48}}, "14": {"start": {"line": 30, "column": 4}, "end": {"line": 34, "column": 7}}, "15": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 85}}, "16": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 61}}, "17": {"start": {"line": 42, "column": 0}, "end": {"line": 68, "column": 3}}, "18": {"start": {"line": 43, "column": 2}, "end": {"line": 67, "column": 3}}, "19": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 51}}, "20": {"start": {"line": 47, "column": 20}, "end": {"line": 47, "column": 67}}, "21": {"start": {"line": 50, "column": 30}, "end": {"line": 61, "column": 7}}, "22": {"start": {"line": 50, "column": 62}, "end": {"line": 61, "column": 6}}, "23": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 32}}, "24": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": 63}}, "25": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 74}}, "26": {"start": {"line": 71, "column": 0}, "end": {"line": 83, "column": 3}}, "27": {"start": {"line": 72, "column": 2}, "end": {"line": 82, "column": 3}}, "28": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 49}}, "29": {"start": {"line": 76, "column": 28}, "end": {"line": 76, "column": 73}}, "30": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 30}}, "31": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 61}}, "32": {"start": {"line": 81, "column": 4}, "end": {"line": 81, "column": 72}}, "33": {"start": {"line": 86, "column": 0}, "end": {"line": 98, "column": 3}}, "34": {"start": {"line": 87, "column": 2}, "end": {"line": 97, "column": 3}}, "35": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 51}}, "36": {"start": {"line": 91, "column": 30}, "end": {"line": 91, "column": 87}}, "37": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 32}}, "38": {"start": {"line": 95, "column": 4}, "end": {"line": 95, "column": 63}}, "39": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 74}}, "40": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 31}, "end": {"line": 9, "column": 36}}, "loc": {"start": {"line": 9, "column": 84}, "end": {"line": 39, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 42, "column": 23}, "end": {"line": 42, "column": 28}}, "loc": {"start": {"line": 42, "column": 77}, "end": {"line": 68, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 50, "column": 42}, "end": {"line": 50, "column": 43}}, "loc": {"start": {"line": 50, "column": 62}, "end": {"line": 61, "column": 6}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 71, "column": 21}, "end": {"line": 71, "column": 26}}, "loc": {"start": {"line": 71, "column": 75}, "end": {"line": 83, "column": 1}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 86, "column": 23}, "end": {"line": 86, "column": 28}}, "loc": {"start": {"line": 86, "column": 77}, "end": {"line": 98, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 4}, "end": {"line": 16, "column": 5}}, "type": "if", "locations": [{"start": {"line": 13, "column": 4}, "end": {"line": 16, "column": 5}}]}, "1": {"loc": {"start": {"line": 13, "column": 8}, "end": {"line": 13, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 8}, "end": {"line": 13, "column": 26}}, {"start": {"line": 13, "column": 30}, "end": {"line": 13, "column": 60}}]}, "2": {"loc": {"start": {"line": 59, "column": 14}, "end": {"line": 59, "column": 70}}, "type": "cond-expr", "locations": [{"start": {"line": 59, "column": 38}, "end": {"line": 59, "column": 55}}, {"start": {"line": 59, "column": 58}, "end": {"line": 59, "column": 70}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/reports.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/reports.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 48}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 49}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 50}}, "5": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 23}}, "6": {"start": {"line": 10, "column": 0}, "end": {"line": 65, "column": 3}}, "7": {"start": {"line": 11, "column": 2}, "end": {"line": 64, "column": 3}}, "8": {"start": {"line": 12, "column": 49}, "end": {"line": 12, "column": 57}}, "9": {"start": {"line": 15, "column": 29}, "end": {"line": 21, "column": 6}}, "10": {"start": {"line": 23, "column": 4}, "end": {"line": 29, "column": 5}}, "11": {"start": {"line": 24, "column": 6}, "end": {"line": 27, "column": 9}}, "12": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 13}}, "13": {"start": {"line": 32, "column": 57}, "end": {"line": 32, "column": 66}}, "14": {"start": {"line": 33, "column": 4}, "end": {"line": 45, "column": 5}}, "15": {"start": {"line": 34, "column": 20}, "end": {"line": 34, "column": 49}}, "16": {"start": {"line": 35, "column": 18}, "end": {"line": 35, "column": 45}}, "17": {"start": {"line": 36, "column": 23}, "end": {"line": 36, "column": 91}}, "18": {"start": {"line": 38, "column": 6}, "end": {"line": 44, "column": 7}}, "19": {"start": {"line": 39, "column": 8}, "end": {"line": 39, "column": 30}}, "20": {"start": {"line": 40, "column": 13}, "end": {"line": 44, "column": 7}}, "21": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 33}}, "22": {"start": {"line": 43, "column": 8}, "end": {"line": 43, "column": 31}}, "23": {"start": {"line": 48, "column": 19}, "end": {"line": 51, "column": 76}}, "24": {"start": {"line": 53, "column": 4}, "end": {"line": 60, "column": 7}}, "25": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 78}}, "26": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 61}}, "27": {"start": {"line": 68, "column": 0}, "end": {"line": 80, "column": 3}}, "28": {"start": {"line": 69, "column": 2}, "end": {"line": 79, "column": 3}}, "29": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 47}}, "30": {"start": {"line": 73, "column": 26}, "end": {"line": 73, "column": 69}}, "31": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 28}}, "32": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 59}}, "33": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 70}}, "34": {"start": {"line": 83, "column": 0}, "end": {"line": 95, "column": 3}}, "35": {"start": {"line": 84, "column": 2}, "end": {"line": 94, "column": 3}}, "36": {"start": {"line": 85, "column": 4}, "end": {"line": 85, "column": 45}}, "37": {"start": {"line": 88, "column": 18}, "end": {"line": 88, "column": 59}}, "38": {"start": {"line": 90, "column": 4}, "end": {"line": 90, "column": 20}}, "39": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 57}}, "40": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 68}}, "41": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 30}}, "loc": {"start": {"line": 10, "column": 78}, "end": {"line": 65, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 68, "column": 22}, "end": {"line": 68, "column": 27}}, "loc": {"start": {"line": 68, "column": 76}, "end": {"line": 80, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 83, "column": 21}, "end": {"line": 83, "column": 26}}, "loc": {"start": {"line": 83, "column": 75}, "end": {"line": 95, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 4}, "end": {"line": 29, "column": 5}}, "type": "if", "locations": [{"start": {"line": 23, "column": 4}, "end": {"line": 29, "column": 5}}]}, "1": {"loc": {"start": {"line": 23, "column": 8}, "end": {"line": 23, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 23, "column": 8}, "end": {"line": 23, "column": 19}}, {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 61}}]}, "2": {"loc": {"start": {"line": 33, "column": 4}, "end": {"line": 45, "column": 5}}, "type": "if", "locations": [{"start": {"line": 33, "column": 4}, "end": {"line": 45, "column": 5}}]}, "3": {"loc": {"start": {"line": 38, "column": 6}, "end": {"line": 44, "column": 7}}, "type": "if", "locations": [{"start": {"line": 38, "column": 6}, "end": {"line": 44, "column": 7}}, {"start": {"line": 40, "column": 13}, "end": {"line": 44, "column": 7}}]}, "4": {"loc": {"start": {"line": 40, "column": 13}, "end": {"line": 44, "column": 7}}, "type": "if", "locations": [{"start": {"line": 40, "column": 13}, "end": {"line": 44, "column": 7}}, {"start": {"line": 42, "column": 13}, "end": {"line": 44, "column": 7}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/workflows.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/routes/workflows.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "3": {"start": {"line": 5, "column": 15}, "end": {"line": 5, "column": 23}}, "4": {"start": {"line": 8, "column": 0}, "end": {"line": 68, "column": 3}}, "5": {"start": {"line": 9, "column": 2}, "end": {"line": 67, "column": 3}}, "6": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 37}}, "7": {"start": {"line": 12, "column": 4}, "end": {"line": 15, "column": 5}}, "8": {"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": 65}}, "9": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 13}}, "10": {"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 58}}, "11": {"start": {"line": 19, "column": 19}, "end": {"line": 19, "column": 43}}, "12": {"start": {"line": 22, "column": 4}, "end": {"line": 25, "column": 5}}, "13": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 71}}, "14": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 13}}, "15": {"start": {"line": 28, "column": 44}, "end": {"line": 28, "column": 46}}, "16": {"start": {"line": 29, "column": 4}, "end": {"line": 38, "column": 5}}, "17": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 92}}, "18": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 90}}, "19": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 78}}, "20": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 84}}, "21": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 82}}, "22": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 69}}, "23": {"start": {"line": 41, "column": 23}, "end": {"line": 41, "column": 36}}, "24": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 85}}, "25": {"start": {"line": 44, "column": 4}, "end": {"line": 56, "column": 5}}, "26": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 33}}, "27": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 97}}, "28": {"start": {"line": 47, "column": 11}, "end": {"line": 56, "column": 5}}, "29": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 30}}, "30": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 78}}, "31": {"start": {"line": 50, "column": 11}, "end": {"line": 56, "column": 5}}, "32": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 31}}, "33": {"start": {"line": 52, "column": 11}, "end": {"line": 56, "column": 5}}, "34": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 31}}, "35": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 113}}, "36": {"start": {"line": 58, "column": 4}, "end": {"line": 63, "column": 7}}, "37": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": 84}}, "38": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 58}}, "39": {"start": {"line": 71, "column": 0}, "end": {"line": 92, "column": 3}}, "40": {"start": {"line": 72, "column": 2}, "end": {"line": 91, "column": 3}}, "41": {"start": {"line": 73, "column": 27}, "end": {"line": 73, "column": 37}}, "42": {"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 5}}, "43": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 65}}, "44": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 13}}, "45": {"start": {"line": 81, "column": 19}, "end": {"line": 81, "column": 58}}, "46": {"start": {"line": 82, "column": 19}, "end": {"line": 82, "column": 43}}, "47": {"start": {"line": 84, "column": 4}, "end": {"line": 87, "column": 7}}, "48": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 84}}, "49": {"start": {"line": 90, "column": 4}, "end": {"line": 90, "column": 75}}, "50": {"start": {"line": 95, "column": 0}, "end": {"line": 107, "column": 3}}, "51": {"start": {"line": 96, "column": 2}, "end": {"line": 106, "column": 3}}, "52": {"start": {"line": 97, "column": 4}, "end": {"line": 97, "column": 48}}, "53": {"start": {"line": 100, "column": 28}, "end": {"line": 100, "column": 73}}, "54": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 30}}, "55": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 61}}, "56": {"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": 72}}, "57": {"start": {"line": 110, "column": 0}, "end": {"line": 122, "column": 3}}, "58": {"start": {"line": 111, "column": 2}, "end": {"line": 121, "column": 3}}, "59": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 47}}, "60": {"start": {"line": 115, "column": 26}, "end": {"line": 115, "column": 69}}, "61": {"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 28}}, "62": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 59}}, "63": {"start": {"line": 120, "column": 4}, "end": {"line": 120, "column": 70}}, "64": {"start": {"line": 125, "column": 0}, "end": {"line": 137, "column": 3}}, "65": {"start": {"line": 126, "column": 2}, "end": {"line": 136, "column": 3}}, "66": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 49}}, "67": {"start": {"line": 130, "column": 28}, "end": {"line": 130, "column": 73}}, "68": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 30}}, "69": {"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 61}}, "70": {"start": {"line": 135, "column": 4}, "end": {"line": 135, "column": 72}}, "71": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 22}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 34}, "end": {"line": 8, "column": 39}}, "loc": {"start": {"line": 8, "column": 87}, "end": {"line": 68, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 71, "column": 34}, "end": {"line": 71, "column": 39}}, "loc": {"start": {"line": 71, "column": 87}, "end": {"line": 92, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 95, "column": 22}, "end": {"line": 95, "column": 27}}, "loc": {"start": {"line": 95, "column": 76}, "end": {"line": 107, "column": 1}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 110, "column": 21}, "end": {"line": 110, "column": 26}}, "loc": {"start": {"line": 110, "column": 75}, "end": {"line": 122, "column": 1}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 125, "column": 23}, "end": {"line": 125, "column": 28}}, "loc": {"start": {"line": 125, "column": 77}, "end": {"line": 137, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 4}, "end": {"line": 15, "column": 5}}, "type": "if", "locations": [{"start": {"line": 12, "column": 4}, "end": {"line": 15, "column": 5}}]}, "1": {"loc": {"start": {"line": 22, "column": 4}, "end": {"line": 25, "column": 5}}, "type": "if", "locations": [{"start": {"line": 22, "column": 4}, "end": {"line": 25, "column": 5}}]}, "2": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 56, "column": 5}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 56, "column": 5}}, {"start": {"line": 47, "column": 11}, "end": {"line": 56, "column": 5}}]}, "3": {"loc": {"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 37}}, {"start": {"line": 44, "column": 41}, "end": {"line": 44, "column": 68}}]}, "4": {"loc": {"start": {"line": 47, "column": 11}, "end": {"line": 56, "column": 5}}, "type": "if", "locations": [{"start": {"line": 47, "column": 11}, "end": {"line": 56, "column": 5}}, {"start": {"line": 50, "column": 11}, "end": {"line": 56, "column": 5}}]}, "5": {"loc": {"start": {"line": 47, "column": 15}, "end": {"line": 47, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 15}, "end": {"line": 47, "column": 40}}, {"start": {"line": 47, "column": 44}, "end": {"line": 47, "column": 70}}]}, "6": {"loc": {"start": {"line": 50, "column": 11}, "end": {"line": 56, "column": 5}}, "type": "if", "locations": [{"start": {"line": 50, "column": 11}, "end": {"line": 56, "column": 5}}, {"start": {"line": 52, "column": 11}, "end": {"line": 56, "column": 5}}]}, "7": {"loc": {"start": {"line": 50, "column": 15}, "end": {"line": 50, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 50, "column": 15}, "end": {"line": 50, "column": 42}}, {"start": {"line": 50, "column": 46}, "end": {"line": 50, "column": 74}}]}, "8": {"loc": {"start": {"line": 52, "column": 11}, "end": {"line": 56, "column": 5}}, "type": "if", "locations": [{"start": {"line": 52, "column": 11}, "end": {"line": 56, "column": 5}}, {"start": {"line": 54, "column": 11}, "end": {"line": 56, "column": 5}}]}, "9": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 5}}, "type": "if", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/services/complianceApi.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/services/complianceApi.ts", "statementMap": {"0": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 23}}, "1": {"start": {"line": 154, "column": 13}, "end": {"line": 510, "column": 2}}, "2": {"start": {"line": 161, "column": 21}, "end": {"line": 167, "column": 6}}, "3": {"start": {"line": 169, "column": 4}, "end": {"line": 171, "column": 5}}, "4": {"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 72}}, "5": {"start": {"line": 173, "column": 4}, "end": {"line": 173, "column": 27}}, "6": {"start": {"line": 181, "column": 21}, "end": {"line": 187, "column": 6}}, "7": {"start": {"line": 189, "column": 4}, "end": {"line": 191, "column": 5}}, "8": {"start": {"line": 190, "column": 6}, "end": {"line": 190, "column": 70}}, "9": {"start": {"line": 193, "column": 4}, "end": {"line": 193, "column": 27}}, "10": {"start": {"line": 205, "column": 21}, "end": {"line": 211, "column": 6}}, "11": {"start": {"line": 213, "column": 4}, "end": {"line": 215, "column": 5}}, "12": {"start": {"line": 214, "column": 6}, "end": {"line": 214, "column": 62}}, "13": {"start": {"line": 217, "column": 4}, "end": {"line": 217, "column": 27}}, "14": {"start": {"line": 222, "column": 21}, "end": {"line": 222, "column": 86}}, "15": {"start": {"line": 224, "column": 4}, "end": {"line": 226, "column": 5}}, "16": {"start": {"line": 225, "column": 6}, "end": {"line": 225, "column": 55}}, "17": {"start": {"line": 228, "column": 4}, "end": {"line": 228, "column": 27}}, "18": {"start": {"line": 233, "column": 21}, "end": {"line": 233, "column": 86}}, "19": {"start": {"line": 235, "column": 4}, "end": {"line": 237, "column": 5}}, "20": {"start": {"line": 236, "column": 6}, "end": {"line": 236, "column": 55}}, "21": {"start": {"line": 239, "column": 4}, "end": {"line": 239, "column": 27}}, "22": {"start": {"line": 244, "column": 21}, "end": {"line": 244, "column": 73}}, "23": {"start": {"line": 246, "column": 4}, "end": {"line": 248, "column": 5}}, "24": {"start": {"line": 247, "column": 6}, "end": {"line": 247, "column": 57}}, "25": {"start": {"line": 250, "column": 4}, "end": {"line": 250, "column": 27}}, "26": {"start": {"line": 255, "column": 21}, "end": {"line": 255, "column": 84}}, "27": {"start": {"line": 257, "column": 4}, "end": {"line": 259, "column": 5}}, "28": {"start": {"line": 258, "column": 6}, "end": {"line": 258, "column": 57}}, "29": {"start": {"line": 261, "column": 4}, "end": {"line": 261, "column": 27}}, "30": {"start": {"line": 265, "column": 21}, "end": {"line": 265, "column": 76}}, "31": {"start": {"line": 267, "column": 4}, "end": {"line": 269, "column": 5}}, "32": {"start": {"line": 268, "column": 6}, "end": {"line": 268, "column": 60}}, "33": {"start": {"line": 271, "column": 4}, "end": {"line": 271, "column": 27}}, "34": {"start": {"line": 275, "column": 21}, "end": {"line": 275, "column": 77}}, "35": {"start": {"line": 277, "column": 4}, "end": {"line": 279, "column": 5}}, "36": {"start": {"line": 278, "column": 6}, "end": {"line": 278, "column": 51}}, "37": {"start": {"line": 281, "column": 4}, "end": {"line": 281, "column": 27}}, "38": {"start": {"line": 285, "column": 21}, "end": {"line": 285, "column": 86}}, "39": {"start": {"line": 287, "column": 4}, "end": {"line": 289, "column": 5}}, "40": {"start": {"line": 288, "column": 6}, "end": {"line": 288, "column": 60}}, "41": {"start": {"line": 291, "column": 4}, "end": {"line": 291, "column": 27}}, "42": {"start": {"line": 296, "column": 21}, "end": {"line": 296, "column": 72}}, "43": {"start": {"line": 298, "column": 4}, "end": {"line": 300, "column": 5}}, "44": {"start": {"line": 299, "column": 6}, "end": {"line": 299, "column": 56}}, "45": {"start": {"line": 302, "column": 4}, "end": {"line": 302, "column": 27}}, "46": {"start": {"line": 311, "column": 21}, "end": {"line": 311, "column": 71}}, "47": {"start": {"line": 313, "column": 4}, "end": {"line": 315, "column": 5}}, "48": {"start": {"line": 314, "column": 6}, "end": {"line": 314, "column": 54}}, "49": {"start": {"line": 317, "column": 4}, "end": {"line": 317, "column": 27}}, "50": {"start": {"line": 321, "column": 21}, "end": {"line": 321, "column": 35}}, "51": {"start": {"line": 322, "column": 4}, "end": {"line": 322, "column": 34}}, "52": {"start": {"line": 323, "column": 4}, "end": {"line": 323, "column": 50}}, "53": {"start": {"line": 325, "column": 21}, "end": {"line": 328, "column": 6}}, "54": {"start": {"line": 330, "column": 4}, "end": {"line": 332, "column": 5}}, "55": {"start": {"line": 331, "column": 6}, "end": {"line": 331, "column": 56}}, "56": {"start": {"line": 334, "column": 4}, "end": {"line": 334, "column": 27}}, "57": {"start": {"line": 339, "column": 21}, "end": {"line": 339, "column": 65}}, "58": {"start": {"line": 341, "column": 4}, "end": {"line": 343, "column": 5}}, "59": {"start": {"line": 342, "column": 6}, "end": {"line": 342, "column": 49}}, "60": {"start": {"line": 345, "column": 4}, "end": {"line": 345, "column": 27}}, "61": {"start": {"line": 354, "column": 21}, "end": {"line": 354, "column": 65}}, "62": {"start": {"line": 356, "column": 4}, "end": {"line": 358, "column": 5}}, "63": {"start": {"line": 357, "column": 6}, "end": {"line": 357, "column": 49}}, "64": {"start": {"line": 360, "column": 4}, "end": {"line": 360, "column": 27}}, "65": {"start": {"line": 364, "column": 21}, "end": {"line": 364, "column": 35}}, "66": {"start": {"line": 365, "column": 4}, "end": {"line": 365, "column": 46}}, "67": {"start": {"line": 366, "column": 4}, "end": {"line": 368, "column": 7}}, "68": {"start": {"line": 367, "column": 6}, "end": {"line": 367, "column": 48}}, "69": {"start": {"line": 370, "column": 21}, "end": {"line": 373, "column": 6}}, "70": {"start": {"line": 375, "column": 4}, "end": {"line": 377, "column": 5}}, "71": {"start": {"line": 376, "column": 6}, "end": {"line": 376, "column": 56}}, "72": {"start": {"line": 379, "column": 4}, "end": {"line": 379, "column": 27}}, "73": {"start": {"line": 384, "column": 21}, "end": {"line": 384, "column": 74}}, "74": {"start": {"line": 386, "column": 4}, "end": {"line": 388, "column": 5}}, "75": {"start": {"line": 387, "column": 6}, "end": {"line": 387, "column": 58}}, "76": {"start": {"line": 390, "column": 4}, "end": {"line": 390, "column": 27}}, "77": {"start": {"line": 399, "column": 21}, "end": {"line": 399, "column": 72}}, "78": {"start": {"line": 401, "column": 4}, "end": {"line": 403, "column": 5}}, "79": {"start": {"line": 402, "column": 6}, "end": {"line": 402, "column": 56}}, "80": {"start": {"line": 405, "column": 4}, "end": {"line": 405, "column": 27}}, "81": {"start": {"line": 414, "column": 21}, "end": {"line": 414, "column": 74}}, "82": {"start": {"line": 416, "column": 4}, "end": {"line": 418, "column": 5}}, "83": {"start": {"line": 417, "column": 6}, "end": {"line": 417, "column": 57}}, "84": {"start": {"line": 420, "column": 4}, "end": {"line": 420, "column": 27}}, "85": {"start": {"line": 427, "column": 21}, "end": {"line": 433, "column": 6}}, "86": {"start": {"line": 435, "column": 4}, "end": {"line": 437, "column": 5}}, "87": {"start": {"line": 436, "column": 6}, "end": {"line": 436, "column": 61}}, "88": {"start": {"line": 439, "column": 4}, "end": {"line": 439, "column": 27}}, "89": {"start": {"line": 444, "column": 21}, "end": {"line": 444, "column": 70}}, "90": {"start": {"line": 446, "column": 4}, "end": {"line": 448, "column": 5}}, "91": {"start": {"line": 447, "column": 6}, "end": {"line": 447, "column": 54}}, "92": {"start": {"line": 450, "column": 4}, "end": {"line": 450, "column": 27}}, "93": {"start": {"line": 460, "column": 21}, "end": {"line": 460, "column": 69}}, "94": {"start": {"line": 462, "column": 4}, "end": {"line": 464, "column": 5}}, "95": {"start": {"line": 463, "column": 6}, "end": {"line": 463, "column": 52}}, "96": {"start": {"line": 466, "column": 4}, "end": {"line": 466, "column": 27}}, "97": {"start": {"line": 471, "column": 21}, "end": {"line": 471, "column": 72}}, "98": {"start": {"line": 473, "column": 4}, "end": {"line": 475, "column": 5}}, "99": {"start": {"line": 474, "column": 6}, "end": {"line": 474, "column": 56}}, "100": {"start": {"line": 477, "column": 4}, "end": {"line": 477, "column": 27}}, "101": {"start": {"line": 487, "column": 21}, "end": {"line": 487, "column": 71}}, "102": {"start": {"line": 489, "column": 4}, "end": {"line": 491, "column": 5}}, "103": {"start": {"line": 490, "column": 6}, "end": {"line": 490, "column": 54}}, "104": {"start": {"line": 493, "column": 4}, "end": {"line": 493, "column": 27}}, "105": {"start": {"line": 502, "column": 21}, "end": {"line": 502, "column": 73}}, "106": {"start": {"line": 504, "column": 4}, "end": {"line": 506, "column": 5}}, "107": {"start": {"line": 505, "column": 6}, "end": {"line": 505, "column": 56}}, "108": {"start": {"line": 508, "column": 4}, "end": {"line": 508, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 156, "column": 2}, "end": {"line": 156, "column": 7}}, "loc": {"start": {"line": 160, "column": 3}, "end": {"line": 174, "column": 3}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 177, "column": 2}, "end": {"line": 177, "column": 7}}, "loc": {"start": {"line": 180, "column": 3}, "end": {"line": 194, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 197, "column": 2}, "end": {"line": 197, "column": 7}}, "loc": {"start": {"line": 204, "column": 3}, "end": {"line": 218, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 221, "column": 2}, "end": {"line": 221, "column": 7}}, "loc": {"start": {"line": 221, "column": 44}, "end": {"line": 229, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 232, "column": 2}, "end": {"line": 232, "column": 7}}, "loc": {"start": {"line": 232, "column": 44}, "end": {"line": 240, "column": 3}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 243, "column": 2}, "end": {"line": 243, "column": 7}}, "loc": {"start": {"line": 243, "column": 27}, "end": {"line": 251, "column": 3}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 254, "column": 2}, "end": {"line": 254, "column": 7}}, "loc": {"start": {"line": 254, "column": 27}, "end": {"line": 262, "column": 3}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 264, "column": 2}, "end": {"line": 264, "column": 7}}, "loc": {"start": {"line": 264, "column": 30}, "end": {"line": 272, "column": 3}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 274, "column": 2}, "end": {"line": 274, "column": 7}}, "loc": {"start": {"line": 274, "column": 21}, "end": {"line": 282, "column": 3}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 284, "column": 2}, "end": {"line": 284, "column": 7}}, "loc": {"start": {"line": 284, "column": 30}, "end": {"line": 292, "column": 3}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 295, "column": 2}, "end": {"line": 295, "column": 7}}, "loc": {"start": {"line": 295, "column": 26}, "end": {"line": 303, "column": 3}}}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 305, "column": 2}, "end": {"line": 305, "column": 7}}, "loc": {"start": {"line": 305, "column": 24}, "end": {"line": 318, "column": 3}}}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 320, "column": 2}, "end": {"line": 320, "column": 7}}, "loc": {"start": {"line": 320, "column": 87}, "end": {"line": 335, "column": 3}}}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 338, "column": 2}, "end": {"line": 338, "column": 7}}, "loc": {"start": {"line": 338, "column": 19}, "end": {"line": 346, "column": 3}}}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 348, "column": 2}, "end": {"line": 348, "column": 7}}, "loc": {"start": {"line": 348, "column": 19}, "end": {"line": 361, "column": 3}}}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 363, "column": 2}, "end": {"line": 363, "column": 7}}, "loc": {"start": {"line": 363, "column": 64}, "end": {"line": 380, "column": 3}}}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 366, "column": 22}, "end": {"line": 366, "column": 23}}, "loc": {"start": {"line": 366, "column": 37}, "end": {"line": 368, "column": 5}}}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 383, "column": 2}, "end": {"line": 383, "column": 7}}, "loc": {"start": {"line": 383, "column": 28}, "end": {"line": 391, "column": 3}}}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 393, "column": 2}, "end": {"line": 393, "column": 7}}, "loc": {"start": {"line": 393, "column": 26}, "end": {"line": 406, "column": 3}}}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 408, "column": 2}, "end": {"line": 408, "column": 7}}, "loc": {"start": {"line": 408, "column": 27}, "end": {"line": 421, "column": 3}}}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 423, "column": 2}, "end": {"line": 423, "column": 7}}, "loc": {"start": {"line": 426, "column": 3}, "end": {"line": 440, "column": 3}}}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 443, "column": 2}, "end": {"line": 443, "column": 7}}, "loc": {"start": {"line": 443, "column": 24}, "end": {"line": 451, "column": 3}}}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 453, "column": 2}, "end": {"line": 453, "column": 7}}, "loc": {"start": {"line": 453, "column": 22}, "end": {"line": 467, "column": 3}}}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 470, "column": 2}, "end": {"line": 470, "column": 7}}, "loc": {"start": {"line": 470, "column": 26}, "end": {"line": 478, "column": 3}}}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 480, "column": 2}, "end": {"line": 480, "column": 7}}, "loc": {"start": {"line": 480, "column": 24}, "end": {"line": 494, "column": 3}}}, "25": {"name": "(anonymous_25)", "decl": {"start": {"line": 496, "column": 2}, "end": {"line": 496, "column": 7}}, "loc": {"start": {"line": 496, "column": 26}, "end": {"line": 509, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 169, "column": 4}, "end": {"line": 171, "column": 5}}, "type": "if", "locations": [{"start": {"line": 169, "column": 4}, "end": {"line": 171, "column": 5}}]}, "1": {"loc": {"start": {"line": 189, "column": 4}, "end": {"line": 191, "column": 5}}, "type": "if", "locations": [{"start": {"line": 189, "column": 4}, "end": {"line": 191, "column": 5}}]}, "2": {"loc": {"start": {"line": 213, "column": 4}, "end": {"line": 215, "column": 5}}, "type": "if", "locations": [{"start": {"line": 213, "column": 4}, "end": {"line": 215, "column": 5}}]}, "3": {"loc": {"start": {"line": 224, "column": 4}, "end": {"line": 226, "column": 5}}, "type": "if", "locations": [{"start": {"line": 224, "column": 4}, "end": {"line": 226, "column": 5}}]}, "4": {"loc": {"start": {"line": 235, "column": 4}, "end": {"line": 237, "column": 5}}, "type": "if", "locations": [{"start": {"line": 235, "column": 4}, "end": {"line": 237, "column": 5}}]}, "5": {"loc": {"start": {"line": 246, "column": 4}, "end": {"line": 248, "column": 5}}, "type": "if", "locations": [{"start": {"line": 246, "column": 4}, "end": {"line": 248, "column": 5}}]}, "6": {"loc": {"start": {"line": 257, "column": 4}, "end": {"line": 259, "column": 5}}, "type": "if", "locations": [{"start": {"line": 257, "column": 4}, "end": {"line": 259, "column": 5}}]}, "7": {"loc": {"start": {"line": 267, "column": 4}, "end": {"line": 269, "column": 5}}, "type": "if", "locations": [{"start": {"line": 267, "column": 4}, "end": {"line": 269, "column": 5}}]}, "8": {"loc": {"start": {"line": 277, "column": 4}, "end": {"line": 279, "column": 5}}, "type": "if", "locations": [{"start": {"line": 277, "column": 4}, "end": {"line": 279, "column": 5}}]}, "9": {"loc": {"start": {"line": 287, "column": 4}, "end": {"line": 289, "column": 5}}, "type": "if", "locations": [{"start": {"line": 287, "column": 4}, "end": {"line": 289, "column": 5}}]}, "10": {"loc": {"start": {"line": 298, "column": 4}, "end": {"line": 300, "column": 5}}, "type": "if", "locations": [{"start": {"line": 298, "column": 4}, "end": {"line": 300, "column": 5}}]}, "11": {"loc": {"start": {"line": 313, "column": 4}, "end": {"line": 315, "column": 5}}, "type": "if", "locations": [{"start": {"line": 313, "column": 4}, "end": {"line": 315, "column": 5}}]}, "12": {"loc": {"start": {"line": 330, "column": 4}, "end": {"line": 332, "column": 5}}, "type": "if", "locations": [{"start": {"line": 330, "column": 4}, "end": {"line": 332, "column": 5}}]}, "13": {"loc": {"start": {"line": 341, "column": 4}, "end": {"line": 343, "column": 5}}, "type": "if", "locations": [{"start": {"line": 341, "column": 4}, "end": {"line": 343, "column": 5}}]}, "14": {"loc": {"start": {"line": 356, "column": 4}, "end": {"line": 358, "column": 5}}, "type": "if", "locations": [{"start": {"line": 356, "column": 4}, "end": {"line": 358, "column": 5}}]}, "15": {"loc": {"start": {"line": 375, "column": 4}, "end": {"line": 377, "column": 5}}, "type": "if", "locations": [{"start": {"line": 375, "column": 4}, "end": {"line": 377, "column": 5}}]}, "16": {"loc": {"start": {"line": 386, "column": 4}, "end": {"line": 388, "column": 5}}, "type": "if", "locations": [{"start": {"line": 386, "column": 4}, "end": {"line": 388, "column": 5}}]}, "17": {"loc": {"start": {"line": 401, "column": 4}, "end": {"line": 403, "column": 5}}, "type": "if", "locations": [{"start": {"line": 401, "column": 4}, "end": {"line": 403, "column": 5}}]}, "18": {"loc": {"start": {"line": 416, "column": 4}, "end": {"line": 418, "column": 5}}, "type": "if", "locations": [{"start": {"line": 416, "column": 4}, "end": {"line": 418, "column": 5}}]}, "19": {"loc": {"start": {"line": 435, "column": 4}, "end": {"line": 437, "column": 5}}, "type": "if", "locations": [{"start": {"line": 435, "column": 4}, "end": {"line": 437, "column": 5}}]}, "20": {"loc": {"start": {"line": 446, "column": 4}, "end": {"line": 448, "column": 5}}, "type": "if", "locations": [{"start": {"line": 446, "column": 4}, "end": {"line": 448, "column": 5}}]}, "21": {"loc": {"start": {"line": 462, "column": 4}, "end": {"line": 464, "column": 5}}, "type": "if", "locations": [{"start": {"line": 462, "column": 4}, "end": {"line": 464, "column": 5}}]}, "22": {"loc": {"start": {"line": 473, "column": 4}, "end": {"line": 475, "column": 5}}, "type": "if", "locations": [{"start": {"line": 473, "column": 4}, "end": {"line": 475, "column": 5}}]}, "23": {"loc": {"start": {"line": 489, "column": 4}, "end": {"line": 491, "column": 5}}, "type": "if", "locations": [{"start": {"line": 489, "column": 4}, "end": {"line": 491, "column": 5}}]}, "24": {"loc": {"start": {"line": 504, "column": 4}, "end": {"line": 506, "column": 5}}, "type": "if", "locations": [{"start": {"line": 504, "column": 4}, "end": {"line": 506, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0], "9": [0], "10": [0], "11": [0], "12": [0], "13": [0], "14": [0], "15": [0], "16": [0], "17": [0], "18": [0], "19": [0], "20": [0], "21": [0], "22": [0], "23": [0], "24": [0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/types/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/types/index.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 29}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 22}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 26}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/compliance-system.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/compliance-system.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 59}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 49}}, "4": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 55}}, "5": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 83}}, "6": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 56}}, "7": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 52}}, "8": {"start": {"line": 25, "column": 20}, "end": {"line": 25, "column": 71}}, "9": {"start": {"line": 26, "column": 4}, "end": {"line": 32, "column": 5}}, "10": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 68}}, "11": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 42}}, "12": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 54}}, "13": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 51}}, "14": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 66}}, "15": {"start": {"line": 37, "column": 23}, "end": {"line": 37, "column": 75}}, "16": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 63}}, "17": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "18": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 64}}, "19": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 70}}, "20": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 58}}, "21": {"start": {"line": 49, "column": 19}, "end": {"line": 49, "column": 72}}, "22": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 62}}, "23": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 52}}, "24": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 95}}, "25": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 34}}, "26": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 83}}, "27": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 63}}, "28": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 31}}, "29": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 53}}, "30": {"start": {"line": 75, "column": 27}, "end": {"line": 75, "column": 70}}, "31": {"start": {"line": 77, "column": 4}, "end": {"line": 88, "column": 5}}, "32": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 59}}, "33": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 34}}, "34": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 30}}, "35": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 55}}, "36": {"start": {"line": 83, "column": 6}, "end": {"line": 87, "column": 8}}, "37": {"start": {"line": 90, "column": 4}, "end": {"line": 90, "column": 57}}, "38": {"start": {"line": 93, "column": 22}, "end": {"line": 93, "column": 72}}, "39": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 34}}, "40": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 57}}, "41": {"start": {"line": 99, "column": 28}, "end": {"line": 99, "column": 75}}, "42": {"start": {"line": 101, "column": 4}, "end": {"line": 112, "column": 5}}, "43": {"start": {"line": 102, "column": 6}, "end": {"line": 102, "column": 59}}, "44": {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 34}}, "45": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 30}}, "46": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 55}}, "47": {"start": {"line": 107, "column": 6}, "end": {"line": 111, "column": 8}}, "48": {"start": {"line": 116, "column": 30}, "end": {"line": 116, "column": 32}}, "49": {"start": {"line": 118, "column": 4}, "end": {"line": 127, "column": 5}}, "50": {"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": 30}}, "51": {"start": {"line": 120, "column": 6}, "end": {"line": 120, "column": 61}}, "52": {"start": {"line": 121, "column": 11}, "end": {"line": 127, "column": 5}}, "53": {"start": {"line": 122, "column": 6}, "end": {"line": 122, "column": 30}}, "54": {"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": 73}}, "55": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 26}}, "56": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 64}}, "57": {"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": 28}}, "58": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 37}}, "59": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 53}}, "60": {"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 51}}, "61": {"start": {"line": 135, "column": 4}, "end": {"line": 135, "column": 48}}, "62": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 95}}, "63": {"start": {"line": 139, "column": 4}, "end": {"line": 139, "column": 42}}, "64": {"start": {"line": 149, "column": 4}, "end": {"line": 149, "column": 102}}, "65": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 63}}, "66": {"start": {"line": 154, "column": 20}, "end": {"line": 154, "column": 79}}, "67": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 63}}, "68": {"start": {"line": 159, "column": 30}, "end": {"line": 159, "column": 77}}, "69": {"start": {"line": 161, "column": 4}, "end": {"line": 161, "column": 60}}, "70": {"start": {"line": 164, "column": 28}, "end": {"line": 164, "column": 93}}, "71": {"start": {"line": 166, "column": 4}, "end": {"line": 166, "column": 55}}, "72": {"start": {"line": 169, "column": 19}, "end": {"line": 175, "column": null}}, "73": {"start": {"line": 178, "column": 4}, "end": {"line": 178, "column": 54}}, "74": {"start": {"line": 179, "column": 4}, "end": {"line": 179, "column": 48}}, "75": {"start": {"line": 180, "column": 4}, "end": {"line": 180, "column": 64}}, "76": {"start": {"line": 182, "column": 4}, "end": {"line": 182, "column": 113}}, "77": {"start": {"line": 184, "column": 4}, "end": {"line": 184, "column": 18}}, "78": {"start": {"line": 200, "column": 23}, "end": {"line": 200, "column": 64}}, "79": {"start": {"line": 201, "column": 4}, "end": {"line": 201, "column": 128}}, "80": {"start": {"line": 203, "column": 4}, "end": {"line": 203, "column": 63}}, "81": {"start": {"line": 206, "column": 20}, "end": {"line": 206, "column": 67}}, "82": {"start": {"line": 209, "column": 28}, "end": {"line": 213, "column": null}}, "83": {"start": {"line": 210, "column": 6}, "end": {"line": 212, "column": null}}, "84": {"start": {"line": 211, "column": 8}, "end": {"line": 212, "column": 71}}, "85": {"start": {"line": 216, "column": 4}, "end": {"line": 216, "column": 63}}, "86": {"start": {"line": 219, "column": 28}, "end": {"line": 219, "column": 91}}, "87": {"start": {"line": 221, "column": 4}, "end": {"line": 221, "column": 71}}, "88": {"start": {"line": 224, "column": 28}, "end": {"line": 224, "column": 83}}, "89": {"start": {"line": 224, "column": 56}, "end": {"line": 224, "column": 75}}, "90": {"start": {"line": 225, "column": 30}, "end": {"line": 225, "column": 87}}, "91": {"start": {"line": 225, "column": 58}, "end": {"line": 225, "column": 79}}, "92": {"start": {"line": 227, "column": 22}, "end": {"line": 227, "column": 27}}, "93": {"start": {"line": 228, "column": 4}, "end": {"line": 232, "column": 5}}, "94": {"start": {"line": 229, "column": 6}, "end": {"line": 229, "column": 27}}, "95": {"start": {"line": 230, "column": 11}, "end": {"line": 232, "column": 5}}, "96": {"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 29}}, "97": {"start": {"line": 235, "column": 26}, "end": {"line": 235, "column": 76}}, "98": {"start": {"line": 235, "column": 63}, "end": {"line": 235, "column": 73}}, "99": {"start": {"line": 237, "column": 4}, "end": {"line": 237, "column": 56}}, "100": {"start": {"line": 239, "column": 19}, "end": {"line": 246, "column": 6}}, "101": {"start": {"line": 248, "column": 4}, "end": {"line": 248, "column": 88}}, "102": {"start": {"line": 249, "column": 4}, "end": {"line": 249, "column": 18}}, "103": {"start": {"line": 255, "column": 4}, "end": {"line": 255, "column": 82}}, "104": {"start": {"line": 258, "column": 20}, "end": {"line": 258, "column": 67}}, "105": {"start": {"line": 259, "column": 28}, "end": {"line": 259, "column": 83}}, "106": {"start": {"line": 262, "column": 4}, "end": {"line": 262, "column": 64}}, "107": {"start": {"line": 263, "column": 4}, "end": {"line": 263, "column": 67}}, "108": {"start": {"line": 265, "column": 4}, "end": {"line": 265, "column": 101}}, "109": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 13}}, "110": {"start": {"line": 12, "column": 15}, "end": {"line": 58, "column": null}}, "111": {"start": {"line": 61, "column": 15}, "end": {"line": 140, "column": null}}, "112": {"start": {"line": 143, "column": 15}, "end": {"line": 185, "column": null}}, "113": {"start": {"line": 188, "column": 15}, "end": {"line": 250, "column": null}}, "114": {"start": {"line": 254, "column": 15}, "end": {"line": 266, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 8}}, "loc": {"start": {"line": 12, "column": 69}, "end": {"line": 58, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": 8}}, "loc": {"start": {"line": 61, "column": 53}, "end": {"line": 140, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 143, "column": 2}, "end": {"line": 143, "column": 8}}, "loc": {"start": {"line": 147, "column": 24}, "end": {"line": 185, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 188, "column": 2}, "end": {"line": 188, "column": 8}}, "loc": {"start": {"line": 192, "column": 3}, "end": {"line": 250, "column": 3}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 209, "column": 43}, "end": {"line": 209, "column": 49}}, "loc": {"start": {"line": 210, "column": 6}, "end": {"line": 212, "column": null}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 210, "column": 34}, "end": {"line": 210, "column": 43}}, "loc": {"start": {"line": 211, "column": 8}, "end": {"line": 212, "column": 71}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 224, "column": 51}, "end": {"line": 224, "column": 52}}, "loc": {"start": {"line": 224, "column": 56}, "end": {"line": 224, "column": 75}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 225, "column": 53}, "end": {"line": 225, "column": 54}}, "loc": {"start": {"line": 225, "column": 58}, "end": {"line": 225, "column": 79}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 235, "column": 58}, "end": {"line": 235, "column": 59}}, "loc": {"start": {"line": 235, "column": 63}, "end": {"line": 235, "column": 73}}}, "9": {"name": "(anonymous_11)", "decl": {"start": {"line": 254, "column": 2}, "end": {"line": 254, "column": 8}}, "loc": {"start": {"line": 254, "column": 79}, "end": {"line": 266, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 26, "column": 4}, "end": {"line": 32, "column": 5}}, "type": "if", "locations": [{"start": {"line": 26, "column": 4}, "end": {"line": 32, "column": 5}}]}, "1": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}]}, "2": {"loc": {"start": {"line": 49, "column": 19}, "end": {"line": 49, "column": 72}}, "type": "cond-expr", "locations": [{"start": {"line": 49, "column": 43}, "end": {"line": 49, "column": 58}}, {"start": {"line": 49, "column": 61}, "end": {"line": 49, "column": 72}}]}, "3": {"loc": {"start": {"line": 77, "column": 4}, "end": {"line": 88, "column": 5}}, "type": "if", "locations": [{"start": {"line": 77, "column": 4}, "end": {"line": 88, "column": 5}}]}, "4": {"loc": {"start": {"line": 101, "column": 4}, "end": {"line": 112, "column": 5}}, "type": "if", "locations": [{"start": {"line": 101, "column": 4}, "end": {"line": 112, "column": 5}}]}, "5": {"loc": {"start": {"line": 118, "column": 4}, "end": {"line": 127, "column": 5}}, "type": "if", "locations": [{"start": {"line": 118, "column": 4}, "end": {"line": 127, "column": 5}}, {"start": {"line": 121, "column": 11}, "end": {"line": 127, "column": 5}}]}, "6": {"loc": {"start": {"line": 121, "column": 11}, "end": {"line": 127, "column": 5}}, "type": "if", "locations": [{"start": {"line": 121, "column": 11}, "end": {"line": 127, "column": 5}}, {"start": {"line": 124, "column": 11}, "end": {"line": 127, "column": 5}}]}, "7": {"loc": {"start": {"line": 149, "column": 75}, "end": {"line": 149, "column": 98}}, "type": "binary-expr", "locations": [{"start": {"line": 149, "column": 75}, "end": {"line": 149, "column": 85}}, {"start": {"line": 149, "column": 89}, "end": {"line": 149, "column": 98}}]}, "8": {"loc": {"start": {"line": 180, "column": 39}, "end": {"line": 180, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 180, "column": 39}, "end": {"line": 180, "column": 49}}, {"start": {"line": 180, "column": 53}, "end": {"line": 180, "column": 62}}]}, "9": {"loc": {"start": {"line": 182, "column": 63}, "end": {"line": 182, "column": 86}}, "type": "binary-expr", "locations": [{"start": {"line": 182, "column": 63}, "end": {"line": 182, "column": 73}}, {"start": {"line": 182, "column": 77}, "end": {"line": 182, "column": 86}}]}, "10": {"loc": {"start": {"line": 200, "column": 23}, "end": {"line": 200, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 200, "column": 23}, "end": {"line": 200, "column": 38}}, {"start": {"line": 200, "column": 42}, "end": {"line": 200, "column": 64}}]}, "11": {"loc": {"start": {"line": 211, "column": 8}, "end": {"line": 212, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 211, "column": 8}, "end": {"line": 211, "column": 68}}, {"start": {"line": 212, "column": 8}, "end": {"line": 212, "column": 71}}]}, "12": {"loc": {"start": {"line": 228, "column": 4}, "end": {"line": 232, "column": 5}}, "type": "if", "locations": [{"start": {"line": 228, "column": 4}, "end": {"line": 232, "column": 5}}, {"start": {"line": 230, "column": 11}, "end": {"line": 232, "column": 5}}]}, "13": {"loc": {"start": {"line": 230, "column": 11}, "end": {"line": 232, "column": 5}}, "type": "if", "locations": [{"start": {"line": 230, "column": 11}, "end": {"line": 232, "column": 5}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 2, "6": 2, "7": 2, "8": 2, "9": 2, "10": 0, "11": 0, "12": 0, "13": 0, "14": 2, "15": 2, "16": 2, "17": 2, "18": 1, "19": 1, "20": 2, "21": 2, "22": 2, "23": 2, "24": 2, "25": 2, "26": 2, "27": 2, "28": 2, "29": 2, "30": 2, "31": 2, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 2, "38": 2, "39": 2, "40": 2, "41": 2, "42": 2, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 2, "49": 2, "50": 1, "51": 1, "52": 1, "53": 0, "54": 0, "55": 1, "56": 1, "57": 2, "58": 2, "59": 2, "60": 2, "61": 2, "62": 2, "63": 2, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 1, "110": 1, "111": 1, "112": 1, "113": 1, "114": 1}, "f": {"0": 2, "1": 2, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0], "1": [1], "2": [1, 1], "3": [0], "4": [0], "5": [1, 1], "6": [0, 1], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/document-processing.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/document-processing.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 59}}, "3": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 27}}, "4": {"start": {"line": 16, "column": 4}, "end": {"line": 19, "column": 5}}, "5": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 91}}, "6": {"start": {"line": 18, "column": 6}, "end": {"line": 18, "column": 19}}, "7": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 69}}, "8": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 16}}, "9": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 83}}, "10": {"start": {"line": 29, "column": 46}, "end": {"line": 29, "column": 48}}, "11": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 27}}, "12": {"start": {"line": 35, "column": 18}, "end": {"line": 35, "column": 69}}, "13": {"start": {"line": 37, "column": 4}, "end": {"line": 61, "column": 5}}, "14": {"start": {"line": 38, "column": 20}, "end": {"line": 38, "column": 50}}, "15": {"start": {"line": 39, "column": 22}, "end": {"line": 39, "column": 51}}, "16": {"start": {"line": 41, "column": 6}, "end": {"line": 60, "column": 7}}, "17": {"start": {"line": 43, "column": 28}, "end": {"line": 46, "column": null}}, "18": {"start": {"line": 49, "column": 8}, "end": {"line": 59, "column": 9}}, "19": {"start": {"line": 50, "column": 10}, "end": {"line": 58, "column": 13}}, "20": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 89}}, "21": {"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}, "22": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 58}}, "23": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 22}}, "24": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 26}}, "25": {"start": {"line": 83, "column": 26}, "end": {"line": 83, "column": 29}}, "26": {"start": {"line": 84, "column": 23}, "end": {"line": 84, "column": 28}}, "27": {"start": {"line": 86, "column": 4}, "end": {"line": 107, "column": 5}}, "28": {"start": {"line": 87, "column": 25}, "end": {"line": 87, "column": 47}}, "29": {"start": {"line": 88, "column": 22}, "end": {"line": 90, "column": null}}, "30": {"start": {"line": 94, "column": 33}, "end": {"line": 97, "column": 8}}, "31": {"start": {"line": 99, "column": 28}, "end": {"line": 100, "column": null}}, "32": {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 47}}, "33": {"start": {"line": 103, "column": 6}, "end": {"line": 106, "column": 7}}, "34": {"start": {"line": 104, "column": 8}, "end": {"line": 104, "column": 28}}, "35": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 14}}, "36": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 24}}, "37": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 85}}, "38": {"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 26}}, "39": {"start": {"line": 119, "column": 31}, "end": {"line": 119, "column": 80}}, "40": {"start": {"line": 119, "column": 54}, "end": {"line": 119, "column": 79}}, "41": {"start": {"line": 120, "column": 27}, "end": {"line": 120, "column": 72}}, "42": {"start": {"line": 120, "column": 50}, "end": {"line": 120, "column": 71}}, "43": {"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}, "44": {"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": 94}}, "45": {"start": {"line": 127, "column": 4}, "end": {"line": 130, "column": 5}}, "46": {"start": {"line": 128, "column": 6}, "end": {"line": 128, "column": 91}}, "47": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 59}}, "48": {"start": {"line": 137, "column": 4}, "end": {"line": 148, "column": 5}}, "49": {"start": {"line": 139, "column": 8}, "end": {"line": 139, "column": 73}}, "50": {"start": {"line": 141, "column": 8}, "end": {"line": 141, "column": 63}}, "51": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 52}}, "52": {"start": {"line": 145, "column": 8}, "end": {"line": 145, "column": 58}}, "53": {"start": {"line": 147, "column": 8}, "end": {"line": 147, "column": 54}}, "54": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 13}}, "55": {"start": {"line": 9, "column": 15}, "end": {"line": 23, "column": null}}, "56": {"start": {"line": 26, "column": 15}, "end": {"line": 71, "column": null}}, "57": {"start": {"line": 74, "column": 15}, "end": {"line": 110, "column": null}}, "58": {"start": {"line": 113, "column": 15}, "end": {"line": 133, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 8}}, "loc": {"start": {"line": 9, "column": 60}, "end": {"line": 23, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 8}}, "loc": {"start": {"line": 26, "column": 61}, "end": {"line": 71, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": 8}}, "loc": {"start": {"line": 77, "column": 21}, "end": {"line": 110, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 99, "column": 52}, "end": {"line": 99, "column": 59}}, "loc": {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 47}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 8}}, "loc": {"start": {"line": 113, "column": 69}, "end": {"line": 133, "column": 3}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 119, "column": 49}, "end": {"line": 119, "column": 50}}, "loc": {"start": {"line": 119, "column": 54}, "end": {"line": 119, "column": 79}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 120, "column": 45}, "end": {"line": 120, "column": 46}}, "loc": {"start": {"line": 120, "column": 50}, "end": {"line": 120, "column": 71}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 136, "column": 2}, "end": {"line": 136, "column": 8}}, "loc": {"start": {"line": 136, "column": 50}, "end": {"line": 149, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 4}, "end": {"line": 19, "column": 5}}, "type": "if", "locations": [{"start": {"line": 16, "column": 4}, "end": {"line": 19, "column": 5}}]}, "1": {"loc": {"start": {"line": 16, "column": 8}, "end": {"line": 16, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 16, "column": 8}, "end": {"line": 16, "column": 25}}, {"start": {"line": 16, "column": 29}, "end": {"line": 16, "column": 58}}]}, "2": {"loc": {"start": {"line": 41, "column": 6}, "end": {"line": 60, "column": 7}}, "type": "if", "locations": [{"start": {"line": 41, "column": 6}, "end": {"line": 60, "column": 7}}]}, "3": {"loc": {"start": {"line": 49, "column": 8}, "end": {"line": 59, "column": 9}}, "type": "if", "locations": [{"start": {"line": 49, "column": 8}, "end": {"line": 59, "column": 9}}]}, "4": {"loc": {"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}, "type": "if", "locations": [{"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}]}, "5": {"loc": {"start": {"line": 103, "column": 6}, "end": {"line": 106, "column": 7}}, "type": "if", "locations": [{"start": {"line": 103, "column": 6}, "end": {"line": 106, "column": 7}}]}, "6": {"loc": {"start": {"line": 103, "column": 10}, "end": {"line": 103, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 103, "column": 10}, "end": {"line": 103, "column": 24}}, {"start": {"line": 103, "column": 28}, "end": {"line": 103, "column": 56}}]}, "7": {"loc": {"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}, "type": "if", "locations": [{"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}]}, "8": {"loc": {"start": {"line": 127, "column": 4}, "end": {"line": 130, "column": 5}}, "type": "if", "locations": [{"start": {"line": 127, "column": 4}, "end": {"line": 130, "column": 5}}]}, "9": {"loc": {"start": {"line": 137, "column": 4}, "end": {"line": 148, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 138, "column": 6}, "end": {"line": 139, "column": 73}}, {"start": {"line": 140, "column": 6}, "end": {"line": 141, "column": 63}}, {"start": {"line": 142, "column": 6}, "end": {"line": 143, "column": 52}}, {"start": {"line": 144, "column": 6}, "end": {"line": 145, "column": 58}}, {"start": {"line": 146, "column": 6}, "end": {"line": 147, "column": 54}}]}}, "s": {"0": 3, "1": 3, "2": 2, "3": 2, "4": 2, "5": 1, "6": 1, "7": 1, "8": 1, "9": 2, "10": 2, "11": 2, "12": 2, "13": 2, "14": 2, "15": 2, "16": 2, "17": 1, "18": 1, "19": 1, "20": 2, "21": 2, "22": 1, "23": 2, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 2, "38": 2, "39": 2, "40": 2, "41": 2, "42": 2, "43": 2, "44": 1, "45": 2, "46": 1, "47": 2, "48": 1, "49": 1, "50": 0, "51": 0, "52": 0, "53": 0, "54": 3, "55": 3, "56": 3, "57": 3, "58": 3}, "f": {"0": 2, "1": 2, "2": 0, "3": 0, "4": 2, "5": 2, "6": 2, "7": 1}, "b": {"0": [1], "1": [2, 2], "2": [1], "3": [1], "4": [1], "5": [0], "6": [0, 0], "7": [1], "8": [1], "9": [1, 0, 0, 0, 0]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/index.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/index.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 36}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 38}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 33}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 36}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/kyc-processing.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/kyc-processing.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 78}}, "2": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 66}}, "3": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 37}}, "4": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 88}}, "5": {"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 115}}, "6": {"start": {"line": 18, "column": 28}, "end": {"line": 18, "column": 100}}, "7": {"start": {"line": 19, "column": 25}, "end": {"line": 19, "column": 90}}, "8": {"start": {"line": 21, "column": 23}, "end": {"line": 24, "column": 46}}, "9": {"start": {"line": 26, "column": 21}, "end": {"line": 26, "column": 38}}, "10": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 144}}, "11": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 36}}, "12": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 86}}, "13": {"start": {"line": 37, "column": 27}, "end": {"line": 37, "column": 66}}, "14": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 37}}, "15": {"start": {"line": 40, "column": 20}, "end": {"line": 40, "column": 21}}, "16": {"start": {"line": 43, "column": 16}, "end": {"line": 43, "column": 99}}, "17": {"start": {"line": 44, "column": 4}, "end": {"line": 45, "column": 39}}, "18": {"start": {"line": 44, "column": 18}, "end": {"line": 44, "column": 34}}, "19": {"start": {"line": 45, "column": 9}, "end": {"line": 45, "column": 39}}, "20": {"start": {"line": 45, "column": 23}, "end": {"line": 45, "column": 39}}, "21": {"start": {"line": 48, "column": 32}, "end": {"line": 48, "column": 53}}, "22": {"start": {"line": 49, "column": 20}, "end": {"line": 49, "column": 68}}, "23": {"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 5}}, "24": {"start": {"line": 50, "column": 54}, "end": {"line": 50, "column": 80}}, "25": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 22}}, "26": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 48}}, "27": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 96}}, "28": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 36}}, "29": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 83}}, "30": {"start": {"line": 66, "column": 27}, "end": {"line": 66, "column": 66}}, "31": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 37}}, "32": {"start": {"line": 70, "column": 28}, "end": {"line": 70, "column": 54}}, "33": {"start": {"line": 71, "column": 21}, "end": {"line": 71, "column": 72}}, "34": {"start": {"line": 73, "column": 19}, "end": {"line": 76, "column": 6}}, "35": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 115}}, "36": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 18}}, "37": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 13}}, "38": {"start": {"line": 8, "column": 15}, "end": {"line": 30, "column": null}}, "39": {"start": {"line": 33, "column": 15}, "end": {"line": 59, "column": null}}, "40": {"start": {"line": 62, "column": 15}, "end": {"line": 80, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 8}}, "loc": {"start": {"line": 8, "column": 49}, "end": {"line": 30, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 8}}, "loc": {"start": {"line": 33, "column": 56}, "end": {"line": 59, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 50, "column": 44}, "end": {"line": 50, "column": 50}}, "loc": {"start": {"line": 50, "column": 54}, "end": {"line": 50, "column": 80}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 8}}, "loc": {"start": {"line": 62, "column": 53}, "end": {"line": 80, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 88}}, "type": "binary-expr", "locations": [{"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 48}}, {"start": {"line": 16, "column": 52}, "end": {"line": 16, "column": 88}}]}, "1": {"loc": {"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 115}}, "type": "binary-expr", "locations": [{"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 56}}, {"start": {"line": 17, "column": 60}, "end": {"line": 17, "column": 115}}]}, "2": {"loc": {"start": {"line": 18, "column": 28}, "end": {"line": 18, "column": 100}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 28}, "end": {"line": 18, "column": 56}}, {"start": {"line": 18, "column": 60}, "end": {"line": 18, "column": 100}}]}, "3": {"loc": {"start": {"line": 19, "column": 25}, "end": {"line": 19, "column": 90}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 25}, "end": {"line": 19, "column": 50}}, {"start": {"line": 19, "column": 54}, "end": {"line": 19, "column": 90}}]}, "4": {"loc": {"start": {"line": 21, "column": 24}, "end": {"line": 21, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 21, "column": 38}, "end": {"line": 21, "column": 41}}, {"start": {"line": 21, "column": 44}, "end": {"line": 21, "column": 45}}]}, "5": {"loc": {"start": {"line": 22, "column": 23}, "end": {"line": 22, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 22, "column": 37}, "end": {"line": 22, "column": 41}}, {"start": {"line": 22, "column": 44}, "end": {"line": 22, "column": 45}}]}, "6": {"loc": {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 23, "column": 41}, "end": {"line": 23, "column": 45}}, {"start": {"line": 23, "column": 48}, "end": {"line": 23, "column": 49}}]}, "7": {"loc": {"start": {"line": 24, "column": 23}, "end": {"line": 24, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 24, "column": 38}, "end": {"line": 24, "column": 41}}, {"start": {"line": 24, "column": 44}, "end": {"line": 24, "column": 45}}]}, "8": {"loc": {"start": {"line": 28, "column": 57}, "end": {"line": 28, "column": 87}}, "type": "cond-expr", "locations": [{"start": {"line": 28, "column": 68}, "end": {"line": 28, "column": 76}}, {"start": {"line": 28, "column": 79}, "end": {"line": 28, "column": 87}}]}, "9": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 45, "column": 39}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 45, "column": 39}}, {"start": {"line": 45, "column": 9}, "end": {"line": 45, "column": 39}}]}, "10": {"loc": {"start": {"line": 45, "column": 9}, "end": {"line": 45, "column": 39}}, "type": "if", "locations": [{"start": {"line": 45, "column": 9}, "end": {"line": 45, "column": 39}}]}, "11": {"loc": {"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 5}}, "type": "if", "locations": [{"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 5}}]}, "12": {"loc": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 81}}, "type": "binary-expr", "locations": [{"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 15}}, {"start": {"line": 50, "column": 19}, "end": {"line": 50, "column": 81}}]}, "13": {"loc": {"start": {"line": 75, "column": 15}, "end": {"line": 75, "column": 74}}, "type": "cond-expr", "locations": [{"start": {"line": 75, "column": 26}, "end": {"line": 75, "column": 62}}, {"start": {"line": 75, "column": 65}, "end": {"line": 75, "column": 74}}]}, "14": {"loc": {"start": {"line": 78, "column": 51}, "end": {"line": 78, "column": 85}}, "type": "cond-expr", "locations": [{"start": {"line": 78, "column": 62}, "end": {"line": 78, "column": 75}}, {"start": {"line": 78, "column": 78}, "end": {"line": 78, "column": 85}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 2, "9": 2, "10": 2, "11": 2, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 0, "19": 1, "20": 0, "21": 1, "22": 1, "23": 1, "24": 3, "25": 0, "26": 1, "27": 1, "28": 1, "29": 2, "30": 2, "31": 2, "32": 2, "33": 2, "34": 2, "35": 2, "36": 2, "37": 2, "38": 2, "39": 2, "40": 2}, "f": {"0": 2, "1": 1, "2": 3, "3": 2}, "b": {"0": [2, 2], "1": [2, 2], "2": [2, 2], "3": [2, 2], "4": [1, 1], "5": [1, 1], "6": [1, 1], "7": [2, 0], "8": [1, 1], "9": [0, 1], "10": [0], "11": [0], "12": [1, 1], "13": [1, 1], "14": [1, 1]}}, "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/report-generation.ts": {"path": "/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/workflows/report-generation.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 59}}, "3": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 27}}, "4": {"start": {"line": 16, "column": 20}, "end": {"line": 16, "column": 67}}, "5": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 83}}, "6": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 19}}, "7": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 52}}, "8": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 27}}, "9": {"start": {"line": 28, "column": 38}, "end": {"line": 28, "column": 40}}, "10": {"start": {"line": 30, "column": 4}, "end": {"line": 45, "column": 5}}, "11": {"start": {"line": 31, "column": 6}, "end": {"line": 44, "column": 7}}, "12": {"start": {"line": 32, "column": 8}, "end": {"line": 43, "column": 9}}, "13": {"start": {"line": 34, "column": 12}, "end": {"line": 34, "column": 91}}, "14": {"start": {"line": 35, "column": 12}, "end": {"line": 35, "column": 96}}, "15": {"start": {"line": 36, "column": 12}, "end": {"line": 36, "column": 18}}, "16": {"start": {"line": 38, "column": 12}, "end": {"line": 38, "column": 84}}, "17": {"start": {"line": 39, "column": 12}, "end": {"line": 39, "column": 18}}, "18": {"start": {"line": 41, "column": 12}, "end": {"line": 41, "column": 85}}, "19": {"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 18}}, "20": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 76}}, "21": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 27}}, "22": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 115}}, "23": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 27}}, "24": {"start": {"line": 64, "column": 20}, "end": {"line": 64, "column": 76}}, "25": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 129}}, "26": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 19}}, "27": {"start": {"line": 85, "column": 4}, "end": {"line": 85, "column": 111}}, "28": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 26}}, "29": {"start": {"line": 90, "column": 23}, "end": {"line": 90, "column": 85}}, "30": {"start": {"line": 91, "column": 37}, "end": {"line": 98, "column": 6}}, "31": {"start": {"line": 101, "column": 4}, "end": {"line": 101, "column": 58}}, "32": {"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": 87}}, "33": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 18}}, "34": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 13}}, "35": {"start": {"line": 9, "column": 15}, "end": {"line": 20, "column": null}}, "36": {"start": {"line": 23, "column": 15}, "end": {"line": 49, "column": null}}, "37": {"start": {"line": 53, "column": 15}, "end": {"line": 69, "column": null}}, "38": {"start": {"line": 72, "column": 15}, "end": {"line": 105, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 8}}, "loc": {"start": {"line": 9, "column": 37}, "end": {"line": 20, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 8}}, "loc": {"start": {"line": 23, "column": 66}, "end": {"line": 49, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 8}}, "loc": {"start": {"line": 53, "column": 61}, "end": {"line": 69, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 8}}, "loc": {"start": {"line": 83, "column": 54}, "end": {"line": 105, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 6}, "end": {"line": 44, "column": 7}}, "type": "if", "locations": [{"start": {"line": 31, "column": 6}, "end": {"line": 44, "column": 7}}]}, "1": {"loc": {"start": {"line": 32, "column": 8}, "end": {"line": 43, "column": 9}}, "type": "switch", "locations": [{"start": {"line": 33, "column": 10}, "end": {"line": 36, "column": 18}}, {"start": {"line": 37, "column": 10}, "end": {"line": 39, "column": 18}}, {"start": {"line": 40, "column": 10}, "end": {"line": 42, "column": 18}}]}, "2": {"loc": {"start": {"line": 59, "column": 53}, "end": {"line": 59, "column": 111}}, "type": "cond-expr", "locations": [{"start": {"line": 59, "column": 65}, "end": {"line": 59, "column": 106}}, {"start": {"line": 59, "column": 109}, "end": {"line": 59, "column": 111}}]}, "3": {"loc": {"start": {"line": 81, "column": 4}, "end": {"line": 81, "column": 62}}, "type": "default-arg", "locations": [{"start": {"line": 81, "column": 53}, "end": {"line": 81, "column": 62}}]}, "4": {"loc": {"start": {"line": 85, "column": 61}, "end": {"line": 85, "column": 84}}, "type": "binary-expr", "locations": [{"start": {"line": 85, "column": 61}, "end": {"line": 85, "column": 71}}, {"start": {"line": 85, "column": 75}, "end": {"line": 85, "column": 84}}]}, "5": {"loc": {"start": {"line": 90, "column": 23}, "end": {"line": 90, "column": 85}}, "type": "cond-expr", "locations": [{"start": {"line": 90, "column": 36}, "end": {"line": 90, "column": 77}}, {"start": {"line": 90, "column": 80}, "end": {"line": 90, "column": 85}}]}}, "s": {"0": 2, "1": 2, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 3, "12": 3, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 2, "35": 2, "36": 2, "37": 2, "38": 2}, "f": {"0": 1, "1": 1, "2": 0, "3": 0}, "b": {"0": [3], "1": [1, 1, 1], "2": [0, 0], "3": [0], "4": [0, 0], "5": [0, 0]}}}