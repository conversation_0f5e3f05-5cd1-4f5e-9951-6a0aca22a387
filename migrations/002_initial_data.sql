-- Migration: 002_initial_data.sql
-- Description: Initial configuration data for Compliance Command Center DBOS
-- Created: 2024-01-09
-- Author: System

-- This migration adds initial configuration data to the database
-- Run this after 001_initial_schema.sql to populate the system with default data

-- Insert initial compliance standards configuration
INSERT INTO compliance_standards_config (standard, display_name, description, monitoring_enabled, notification_enabled) VALUES
('SEC', 'Securities and Exchange Commission', 'U.S. federal agency that enforces securities laws', true, true),
('GLBA', 'Gramm-Leach-Bliley Act', 'Financial privacy rule for financial institutions', true, true),
('SOX', 'Sarbanes-Oxley Act', 'Federal law for public company auditing and financial regulations', true, true),
('GDPR', 'General Data Protection Regulation', 'EU regulation on data protection and privacy', true, true),
('CCPA', 'California Consumer Privacy Act', 'California privacy rights statute', true, true),
('FINRA', 'Financial Industry Regulatory Authority', 'Self-regulatory organization for brokerage firms', true, true)
ON CONFLICT (standard) DO NOTHING;

-- Insert default compliance rules
INSERT INTO compliance_rules (rule_id, standard, rule_type, description, pattern, severity, is_active) VALUES
('SEC-001', 'SEC', 'financial_disclosure', 'Financial statements must include quarterly earnings disclosure', 'quarterly.*(earnings|revenue|income)', 'high', true),
('GLBA-001', 'GLBA', 'privacy', 'Customer financial information must be protected', '(ssn|social.security|account.number|routing.number)', 'critical', true),
('SOX-001', 'SOX', 'financial_disclosure', 'Internal controls must be documented', 'internal.control.*documentation', 'high', true),
('GDPR-001', 'GDPR', 'data_protection', 'Personal data processing must have legal basis', '(personal.data|processing|consent)', 'high', true),
('CCPA-001', 'CCPA', 'privacy', 'Consumer privacy rights must be disclosed', '(consumer.rights|privacy.policy|data.sale)', 'medium', true),
('SEC-002', 'SEC', 'financial_disclosure', 'Material changes must be disclosed within required timeframes', '(material.change|significant.event)', 'high', true),
('GLBA-002', 'GLBA', 'data_protection', 'Customer data must be encrypted in transit and at rest', '(encryption|secure.transmission)', 'high', true),
('SOX-002', 'SOX', 'security', 'Access controls must be documented and regularly reviewed', '(access.control|user.privilege)', 'medium', true),
('GDPR-002', 'GDPR', 'privacy', 'Data subject rights must be clearly communicated', '(data.subject.rights|right.to.erasure)', 'medium', true),
('CCPA-002', 'CCPA', 'data_protection', 'Personal information categories must be disclosed', '(personal.information|data.category)', 'medium', true)
ON CONFLICT (rule_id) DO NOTHING;

-- Insert system configuration
INSERT INTO system_configuration (config_key, config_value, config_type, description, category) VALUES
('compliance.auto_scan_enabled', 'true', 'boolean', 'Enable automatic compliance scanning', 'compliance'),
('kyc.risk_threshold_high', '70', 'number', 'Risk score threshold for high-risk profiles', 'kyc'),
('kyc.risk_threshold_critical', '85', 'number', 'Risk score threshold for critical-risk profiles', 'kyc'),
('reporting.auto_generation_enabled', 'true', 'boolean', 'Enable automatic report generation', 'reporting'),
('notifications.email_enabled', 'true', 'boolean', 'Enable email notifications', 'system'),
('workflow.max_retry_attempts', '3', 'number', 'Maximum retry attempts for workflows', 'system'),
('compliance.violation_retention_days', '2555', 'number', 'Violation records retention (7 years)', 'compliance'),
('audit.log_retention_days', '2555', 'number', 'Audit logs retention (7 years)', 'system'),
('kyc.identity_verification_enabled', 'true', 'boolean', 'Enable identity verification for KYC profiles', 'kyc'),
('kyc.sanctions_check_enabled', 'true', 'boolean', 'Enable sanctions checking for KYC profiles', 'kyc'),
('reporting.default_format', 'PDF', 'string', 'Default format for generated reports', 'reporting'),
('notifications.violation_alert_threshold', 'high', 'string', 'Minimum severity for violation alerts', 'system'),
('compliance.document_retention_days', '2555', 'number', 'Document retention period (7 years)', 'compliance'),
('workflow.timeout_minutes', '30', 'number', 'Default workflow timeout in minutes', 'system'),
('system.maintenance_window_start', '02:00', 'string', 'Daily maintenance window start time (UTC)', 'system'),
('system.maintenance_window_end', '04:00', 'string', 'Daily maintenance window end time (UTC)', 'system')
ON CONFLICT (config_key) DO NOTHING;

-- Insert initial performance metrics configuration
INSERT INTO performance_metrics (metric_name, metric_category, metric_value, metric_unit, aggregation_level) VALUES
('compliance_rate_baseline', 'compliance', 95.0, 'percentage', 'daily'),
('avg_document_processing_time', 'workflow', 120.0, 'seconds', 'hourly'),
('kyc_approval_rate', 'kyc', 85.0, 'percentage', 'daily'),
('violation_detection_rate', 'compliance', 12.5, 'percentage', 'daily'),
('system_uptime', 'system', 99.9, 'percentage', 'daily');

-- Verify data installation
DO $$
DECLARE
    standards_count INTEGER;
    rules_count INTEGER;
    config_count INTEGER;
BEGIN
    -- Count standards
    SELECT COUNT(*) INTO standards_count FROM compliance_standards_config;
    
    -- Count rules
    SELECT COUNT(*) INTO rules_count FROM compliance_rules WHERE is_active = true;
    
    -- Count configuration
    SELECT COUNT(*) INTO config_count FROM system_configuration WHERE is_active = true;
    
    RAISE NOTICE 'Initial data migration completed successfully!';
    RAISE NOTICE 'Loaded % compliance standards, % active rules, % configuration settings', 
                 standards_count, rules_count, config_count;
    RAISE NOTICE 'System is ready for compliance operations';
END $$;
