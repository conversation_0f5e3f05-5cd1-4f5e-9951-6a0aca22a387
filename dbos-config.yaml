name: dbos_kyc_demo
database_url: ${DBOS_DATABASE_URL}
database:
  app_db_client: pg-node
  migrate:
    - "node scripts/migrate.js"
runtimeConfig:
  # Pre-deployment hooks - run before the application starts
  predeploy:
    - name: "Pre-deployment Setup"
      command: "node scripts/predeploy.js"
      description: "Load mock data and verify database setup"

  # Post-deployment hooks - run after successful deployment
  postdeploy:
    - name: "Post-deployment Verification"
      command: "node scripts/postdeploy.js"
      description: "Perform health checks and validate system configuration"

  # Application startup
  start:
    - "npm start"
